/**
 * Dhan API Service for REST API calls
 * Handles authentication and API requests to Dhan
 */

export interface DhanExpiryResponse {
  data: string[]; // Array of expiry dates in YYYY-MM-DD format
  status: string;
}

export interface DhanOptionChainRequest {
  UnderlyingScrip: number;
  UnderlyingSeg: string;
}

export class DhanAPIService {
  private accessToken: string;
  private clientId: string;
  private baseUrl: string = 'https://api.dhan.co';
  private expiryCache: Map<string, { data: string[], timestamp: number }> = new Map();
  private cacheTimeout: number = 5 * 60 * 1000; // 5 minutes cache

  constructor(accessToken?: string, clientId?: string) {
    this.accessToken = accessToken || process.env.ACCESS_TOKEN || '';
    this.clientId = clientId || process.env.CLIENT_ID || '';

    if (!this.accessToken || !this.clientId) {
      console.warn('⚠️ Dhan API credentials not provided');
    }
  }

  /**
   * Get expiry dates for an underlying instrument
   */
  async getExpiryDates(underlyingScrip: number, underlyingSeg: string): Promise<string[]> {
    try {
      const cacheKey = `${underlyingScrip}_${underlyingSeg}`;

      // Check cache first
      const cached = this.expiryCache.get(cacheKey);
      if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
        console.log('📋 Using cached expiry dates for', cacheKey);
        return cached.data;
      }

      if (!this.accessToken || !this.clientId) {
        console.warn('⚠️ No Dhan API credentials - returning mock expiry dates');
        const mockData = this.getMockExpiryDates();
        this.expiryCache.set(cacheKey, { data: mockData, timestamp: Date.now() });
        return mockData;
      }

      const requestBody: DhanOptionChainRequest = {
        UnderlyingScrip: underlyingScrip,
        UnderlyingSeg: underlyingSeg
      };

      console.log('🔍 Fetching expiry dates from Dhan API:', requestBody);

      const response = await fetch(`${this.baseUrl}/v2/optionchain/expirylist`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'access-token': this.accessToken,
          'client-id': this.clientId,
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Dhan API error: ${response.status} ${response.statusText}`);
      }

      const data: DhanExpiryResponse = await response.json();
      console.log('✅ Received expiry dates:', data);

      const expiryDates = data.data || [];

      // Cache the result
      this.expiryCache.set(cacheKey, { data: expiryDates, timestamp: Date.now() });

      return expiryDates;

    } catch (error) {
      console.error('❌ Error fetching expiry dates:', error);
      // Return mock data as fallback
      const mockData = this.getMockExpiryDates();
      const cacheKey = `${underlyingScrip}_${underlyingSeg}`;
      this.expiryCache.set(cacheKey, { data: mockData, timestamp: Date.now() });
      return mockData;
    }
  }

  /**
   * Mock expiry dates for development/fallback
   */
  private getMockExpiryDates(): string[] {
    const today = new Date();
    const expiries: string[] = [];

    // Generate next 6 weekly expiries (Thursdays)
    for (let i = 0; i < 6; i++) {
      const nextThursday = new Date(today);
      const daysUntilThursday = (4 - today.getDay() + 7) % 7 || 7; // Thursday is day 4
      nextThursday.setDate(today.getDate() + daysUntilThursday + (i * 7));
      expiries.push(nextThursday.toISOString().split('T')[0]);
    }

    // Add monthly expiries (last Thursday of month)
    for (let i = 1; i <= 3; i++) {
      const monthlyExpiry = new Date(today.getFullYear(), today.getMonth() + i, 1);
      // Find last Thursday of the month
      monthlyExpiry.setMonth(monthlyExpiry.getMonth() + 1, 0); // Last day of month
      const lastDay = monthlyExpiry.getDate();
      const lastDayOfWeek = monthlyExpiry.getDay();
      const lastThursday = lastDay - ((lastDayOfWeek + 3) % 7);
      monthlyExpiry.setDate(lastThursday);
      
      const expiryStr = monthlyExpiry.toISOString().split('T')[0];
      if (!expiries.includes(expiryStr)) {
        expiries.push(expiryStr);
      }
    }

    return expiries.sort();
  }

  /**
   * Check if API credentials are available
   */
  hasCredentials(): boolean {
    return !!(this.accessToken && this.clientId);
  }

  /**
   * Get API status
   */
  getStatus(): { hasCredentials: boolean; baseUrl: string } {
    return {
      hasCredentials: this.hasCredentials(),
      baseUrl: this.baseUrl
    };
  }
}

// Singleton instance
let dhanAPIService: DhanAPIService | null = null;

export function getDhanAPIService(): DhanAPIService {
  if (!dhanAPIService) {
    dhanAPIService = new DhanAPIService();
  }
  return dhanAPIService;
}
