// Check CSV headers
const fs = require('fs');
const csv = require('csv-parser');

console.log('🔍 Checking CSV headers...');

let headersPrinted = false;
let rowCount = 0;

fs.createReadStream('./instruments.csv')
  .pipe(csv())
  .on('headers', (headers) => {
    console.log('📋 CSV Headers found:');
    headers.forEach((header, index) => {
      console.log(`${index + 1}. "${header}"`);
    });
    console.log(`\nTotal headers: ${headers.length}`);
  })
  .on('data', (data) => {
    rowCount++;
    if (!headersPrinted) {
      console.log('\n🔍 First row data:');
      console.log('Keys:', Object.keys(data));
      console.log('Sample values:');
      Object.entries(data).slice(0, 10).forEach(([key, value]) => {
        console.log(`  ${key}: "${value}"`);
      });
      headersPrinted = true;
    }
    
    if (rowCount >= 5) {
      process.exit(0);
    }
  })
  .on('error', (error) => {
    console.error('❌ Error:', error);
  });
