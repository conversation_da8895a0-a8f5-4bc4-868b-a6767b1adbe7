(()=>{var e={};e.id=403,e.ids=[403],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},1282:e=>{"use strict";e.exports=require("child_process")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},9801:e=>{"use strict";e.exports=require("os")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},4175:e=>{"use strict";e.exports=require("tty")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},3215:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c}),a(308),a(2029),a(5866);var r=a(3191),s=a(8716),n=a(7922),o=a.n(n),i=a(5231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c=["",{children:["option-chain",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,308)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,2029)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\option-chain\\page.tsx"],m="/option-chain/page",x={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/option-chain/page",pathname:"/option-chain",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1834:(e,t,a)=>{Promise.resolve().then(a.bind(a,3650))},3650:(e,t,a)=>{"use strict";let r,s;a.r(t),a.d(t,{default:()=>f});var n=a(326),o=a(7577),i=a(1223),l=a(5767);function c({marketData:e}){let[t,a]=(0,o.useState)(null),[r,s]=(0,o.useState)(""),[c,d]=(0,o.useState)(null),[m,x]=(0,o.useState)(!1),[h,u]=(0,o.useState)(null),[p,g]=(0,o.useState)(0),[y,f]=(0,o.useState)(!1),v=async()=>{try{x(!0),u(null);let e=await l.qn.getCachedStaticData(l.A_.EXPIRY_DATES);if(e&&"object"==typeof e&&"expiries"in e&&Array.isArray(e.expiries)){console.log("✅ OptionChain: Using cached expiry data"),a(e),e.expiries.length>0&&s(e.expiries[0]),x(!1);return}console.log("\uD83C\uDF10 OptionChain: Fetching fresh expiry data from API");let t=await fetch("/api/nifty-expiry");if(!t.ok)throw Error(`Failed to fetch expiry dates: ${t.statusText}`);let r=await t.json();if(r.success)a(r.data),await l.qn.cacheStaticData(l.A_.EXPIRY_DATES,r.data),console.log("\uD83D\uDCBE OptionChain: Cached expiry data"),r.data.expiries.length>0&&s(r.data.expiries[0]);else throw Error(r.message||"Failed to fetch expiry dates")}catch(e){console.error("❌ OptionChain: Error fetching expiry dates:",e),u(e instanceof Error?e.message:"Unknown error")}finally{x(!1)}},b=i.A1.price,S=i.A1.number,D=e=>e?e>0?"text-green-400":e<0?"text-red-400":"text-gray-400":"text-gray-400";return m?n.jsx("div",{className:"flex items-center justify-center p-8",children:(0,n.jsxs)("div",{className:"text-center",children:[n.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),n.jsx("p",{className:"text-gray-600",children:"Loading option chain..."})]})}):h?(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[n.jsx("h3",{className:"text-red-800 font-medium",children:"Error Loading Option Chain"}),n.jsx("p",{className:"text-red-600 text-sm mt-1",children:h}),n.jsx("button",{onClick:v,className:"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700",children:"Retry"})]}):(0,n.jsxs)("div",{className:"bg-white min-h-screen",children:[n.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-6",children:[n.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Options"}),(0,n.jsxs)("div",{className:"flex space-x-1",children:[n.jsx("button",{className:"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md",children:"\uD83D\uDD0D NIFTY"}),n.jsx("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Strategy builder"}),n.jsx("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Class"}),n.jsx("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Volatility"})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx("div",{className:"text-sm text-gray-600",children:"By expiration | by strike"}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[n.jsx("span",{className:"text-sm text-gray-600",children:"NIFTY Spot:"}),n.jsx("span",{className:"text-lg font-bold text-gray-900",children:b(p)}),24850===p&&n.jsx("span",{className:"text-xs text-gray-500",children:"(Mock)"})]})]})]})}),t&&(0,n.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[n.jsx("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2",children:t.expiries.slice(0,15).map((e,t)=>{let a=new Date(e),o=e===r,i=a.toDateString()===new Date().toDateString(),l=6048e5>Math.abs(a.getTime()-new Date().getTime()),c=new Date().getFullYear(),d=a.getFullYear(),m=a.toLocaleDateString("en-US",{month:"short"}),x=a.getDate();return n.jsx("button",{onClick:()=>s(e),className:`flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ${o?"bg-black text-white border-black":i?"bg-orange-500 text-white border-orange-500":l?"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"}`,children:(0,n.jsxs)("div",{className:"text-center min-w-[40px]",children:[(0,n.jsxs)("div",{className:"text-xs font-normal text-gray-600",children:[m,d!==c&&` ${d.toString().slice(-2)}`]}),n.jsx("div",{className:"font-bold text-sm",children:x})]})},e)})}),!1]}),c&&(0,n.jsxs)("div",{className:"bg-white",children:[(0,n.jsxs)("div",{className:"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider",children:[n.jsx("div",{className:"flex-1 text-center py-3 border-r border-gray-200",children:n.jsx("span",{className:"text-green-600 font-bold",children:"Calls"})}),n.jsx("div",{className:"w-20 text-center py-3 border-r border-gray-200",children:n.jsx("span",{className:"font-bold",children:"Strike"})}),n.jsx("div",{className:"flex-1 text-center py-3",children:n.jsx("span",{className:"text-red-600 font-bold",children:"Puts"})})]}),(0,n.jsxs)("div",{className:"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider",children:[n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"OI"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1 border-r border-gray-200",children:"LTP"}),n.jsx("div",{className:"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold",children:"Strike"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"LTP"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),n.jsx("div",{className:"flex-1 text-center py-2 px-1",children:"OI"})]}),n.jsx("div",{className:"divide-y divide-gray-100",children:c.rows.map((e,t)=>{let a=50>=Math.abs(e.strikePrice-p),r=e.strikePrice<p,s=e.strikePrice>p;return(0,n.jsxs)("div",{className:`flex hover:bg-gray-50 transition-colors ${a?"bg-yellow-50":t%2==0?"bg-white":"bg-gray-25"}`,children:[n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm ${r?"text-green-700 font-medium":"text-gray-700"}`,children:S(e.call?.openInterest)}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm ${r?"text-green-700 font-medium":"text-gray-700"}`,children:S(e.call?.volume)}),n.jsx("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:b(e.call?.bid)}),n.jsx("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:b(e.call?.ask)}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm font-medium ${D(e.call?.change)}`,children:e.call?.change?(0,n.jsxs)(n.Fragment,{children:[e.call.change>0?"+":"",e.call.change.toFixed(2),(0,n.jsxs)("div",{className:"text-xs",children:["(",e.call.changePercent>0?"+":"",e.call.changePercent.toFixed(1),"%)"]})]}):"-"}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${r?"text-green-600":"text-gray-700"}`,children:b(e.call?.ltp)}),n.jsx("div",{className:`w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ${a?"bg-yellow-100 text-yellow-800":"text-gray-900"}`,children:e.strikePrice}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm font-bold ${s?"text-red-600":"text-gray-700"}`,children:b(e.put?.ltp)}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm font-medium ${D(e.put?.change)}`,children:e.put?.change?(0,n.jsxs)(n.Fragment,{children:[e.put.change>0?"+":"",e.put.change.toFixed(2),(0,n.jsxs)("div",{className:"text-xs",children:["(",e.put.changePercent>0?"+":"",e.put.changePercent.toFixed(1),"%)"]})]}):"-"}),n.jsx("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:b(e.put?.ask)}),n.jsx("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:b(e.put?.bid)}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm ${s?"text-red-700 font-medium":"text-gray-700"}`,children:S(e.put?.volume)}),n.jsx("div",{className:`flex-1 text-center py-3 px-1 text-sm ${s?"text-red-700 font-medium":"text-gray-700"}`,children:S(e.put?.openInterest)})]},e.strikePrice)})})]}),c&&n.jsx("div",{className:"bg-gray-50 border-t border-gray-200 px-6 py-4",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[n.jsx("span",{children:"Show all"}),n.jsx("span",{children:"•"}),(0,n.jsxs)("span",{children:["Showing ",c.rows.length," strikes around ATM"]})]}),(0,n.jsxs)("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(c.timestamp).toLocaleTimeString()]})]})})]})}let d=e=>{let t;let a=new Set,r=(e,r)=>{let s="function"==typeof e?e(t):e;if(!Object.is(s,t)){let e=t;t=(null!=r?r:"object"!=typeof s||null===s)?s:Object.assign({},t,s),a.forEach(a=>a(t,e))}},s=()=>t,n={setState:r,getState:s,getInitialState:()=>o,subscribe:e=>(a.add(e),()=>a.delete(e))},o=t=e(r,s,n);return n},m=e=>e?d(e):d,x=e=>e;function h(e,t){let a;try{a=e()}catch(e){return}return{getItem:e=>{var r;let s=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),n=null!=(r=a.getItem(e))?r:null;return n instanceof Promise?n.then(s):s(n)},setItem:(e,r)=>a.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>a.removeItem(e)}}let u=e=>t=>{try{let a=e(t);if(a instanceof Promise)return a;return{then:e=>u(e)(a),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>u(t)(e)}}};var p=a(3687);let g=(e=>{let t=m(e),a=e=>(function(e,t=x){let a=o.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return o.useDebugValue(a),a})(t,e);return Object.assign(a,t),a})((r=(e,t)=>({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1,wsManager:null,setMarketData:a=>{e({marketData:a.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:new Date,isLoading:!1}),t().saveToLocalStorage()},updateMarketData:t=>e(e=>({marketData:{...e.marketData,[t.securityId]:{...e.marketData[t.securityId],...t}}})),updateMarketDataBatch:t=>e(e=>{let a={...e.marketData};for(let e of t)a[e.securityId]={...a[e.securityId],...e};return{marketData:a}}),hydrateFromRedis:t=>e({marketData:t.reduce((e,t)=>(e[t.securityId]=t,e),{})}),setConnectionStatus:a=>{e({connectionStatus:a,isConnected:"connected"===a,error:"error"===a?t().error:null})},setError:t=>e({error:t}),setLoading:t=>e({isLoading:t}),setCacheLoaded:t=>e({cacheLoaded:t}),initializeWebSocket:()=>{let a=p.ZP.getInstance();e({wsManager:a}),a.connect({onConnect:()=>{console.log("\uD83D\uDD0C MarketStore: WebSocket connected"),t().setConnectionStatus("connected")},onDisconnect:e=>{console.log("\uD83D\uDD0C MarketStore: WebSocket disconnected:",e),t().setConnectionStatus("disconnected")},onError:e=>{console.error("❌ MarketStore: WebSocket error:",e),t().setConnectionStatus("error"),t().setError(e.message||"WebSocket connection error")},onReconnect:e=>{console.log("\uD83D\uDD04 MarketStore: WebSocket reconnected after",e,"attempts"),t().setConnectionStatus("connected"),t().setError(null)},onMarketData:e=>{e&&"object"==typeof e&&t().updateMarketData(e)},onMarketDataBatch:e=>{Array.isArray(e)&&e.length>0&&t().updateMarketDataBatch(e)}})},connect:()=>{let{wsManager:e}=t();e&&!e.isConnected()&&(t().setConnectionStatus("connecting"),console.log("\uD83D\uDD0C MarketStore: Connection request - WebSocket manager will handle"))},disconnect:()=>{let{wsManager:e}=t();e&&(e.disconnect(),t().setConnectionStatus("disconnected"))},loadFromCache:async()=>{try{e({isLoading:!0}),console.log("\uD83D\uDCD6 MarketStore: Loading cached market data...");let a=t().loadFromLocalStorage(),r=await fetch("/api/cache/bulk");if(r.ok){let e=await r.json();e.success&&e.data&&Array.isArray(e.data)&&e.data.length>0?(t().setMarketData(e.data),console.log(`✅ MarketStore: Loaded ${e.data.length} items from Redis cache`)):a||console.log("\uD83D\uDCED MarketStore: No cached data found")}else a||console.log("\uD83D\uDCED MarketStore: Failed to load cached data");e({cacheLoaded:!0,isLoading:!1})}catch(t){console.error("❌ MarketStore: Failed to load cached data:",t),e({cacheLoaded:!0,isLoading:!1})}},refreshFromCache:async()=>{try{let e=await fetch("/api/cache/bulk");if(e.ok){let a=await e.json();a.success&&a.data&&Array.isArray(a.data)&&a.data.length>0&&(t().setMarketData(a.data),console.log(`✅ MarketStore: Refreshed ${a.data.length} items from cache`))}}catch(e){console.error("❌ MarketStore: Failed to refresh from cache:",e)}},clearCache:async()=>{try{(await fetch("/api/cache/clear",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pattern:"market_data:*"})})).ok&&(e({marketData:{},lastUpdate:null}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp"),console.log("\uD83E\uDDF9 MarketStore: Cache cleared"))}catch(e){console.error("❌ MarketStore: Failed to clear cache:",e)}},saveToLocalStorage:()=>{let{marketData:e}=t();if(Object.keys(e).length>0)try{localStorage.setItem("marketData",JSON.stringify(Object.values(e))),localStorage.setItem("marketDataTimestamp",new Date().toISOString())}catch(e){console.warn("⚠️ MarketStore: Failed to save to localStorage:",e)}},loadFromLocalStorage:()=>{try{let t=localStorage.getItem("marketData"),a=localStorage.getItem("marketDataTimestamp");if(t&&a){let r=JSON.parse(t),s=new Date(a),n=(new Date().getTime()-s.getTime())/6e4;if(n<10&&Array.isArray(r)&&r.length>0)return e({marketData:r.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:s,isLoading:!1}),console.log(`⚡ MarketStore: Loaded ${r.length} items from localStorage (${n.toFixed(1)}min old)`),!0}}catch(e){console.warn("⚠️ MarketStore: Failed to load from localStorage:",e),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")}return!1},reset:()=>{e({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")},getMarketDataBySymbol:e=>Object.values(t().marketData).find(t=>t.symbol===e),getMarketDataBySecurityId:e=>t().marketData[e]}),s={name:"market-store",storage:h(()=>localStorage),partialize:e=>({marketData:e.marketData})},(e,t,a)=>{let n,o={storage:h(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...s},i=!1,l=new Set,c=new Set,d=o.storage;if(!d)return r((...t)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),e(...t)},t,a);let m=()=>{let e=o.partialize({...t()});return d.setItem(o.name,{state:e,version:o.version})},x=a.setState;a.setState=(e,t)=>{x(e,t),m()};let p=r((...t)=>{e(...t),m()},t,a);a.getInitialState=()=>p;let g=()=>{var a,r;if(!d)return;i=!1,l.forEach(e=>{var a;return e(null!=(a=t())?a:p)});let s=(null==(r=o.onRehydrateStorage)?void 0:r.call(o,null!=(a=t())?a:p))||void 0;return u(d.getItem.bind(d))(o.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(a=>{var r;let[s,i]=a;if(e(n=o.merge(i,null!=(r=t())?r:p),!0),s)return m()}).then(()=>{null==s||s(n,void 0),n=t(),i=!0,c.forEach(e=>e(n))}).catch(e=>{null==s||s(void 0,e)})};return a.persist={setOptions:e=>{o={...o,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>g(),hasHydrated:()=>i,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||g(),n||p})),y=()=>{let e=g();return(0,o.useEffect)(()=>{fetch("/api/cache/all-latest").then(e=>e.json()).then(({data:t})=>{Array.isArray(t)&&e.hydrateFromRedis(t)})},[e.hydrateFromRedis]),{marketData:Object.values(e.marketData),updateMarketData:e.updateMarketData,updateMarketDataBatch:e.updateMarketDataBatch,hydrateFromRedis:e.hydrateFromRedis}};function f(){let{marketData:e}=y(),t=new Map;return e.forEach(e=>{e.securityId&&t.set(e.securityId,e)}),(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[n.jsx("div",{className:"bg-white border-b border-gray-200 px-6 py-3",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx("a",{href:"/",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"← Main Dashboard"}),n.jsx("a",{href:"/subscribed",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"\uD83D\uDCCA Subscribed Data"})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," instruments"]}),(0,n.jsxs)("span",{className:"text-sm text-gray-600",children:[e.filter(e=>(e.ltp??0)>0).length," active"]})]})]})}),n.jsx(c,{marketData:t})]})}},5767:(e,t,a)=>{"use strict";a.d(t,{A_:()=>o,qn:()=>i});var r=a(3566);class s{static{this.instance=null}isBrowser(){return!1}static getInstance(){return s.instance||(s.instance=new s),s.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch{this.compressionSupported=!1}}async set(e,t,a={}){try{if(!this.isBrowser())return console.log(`⚠️ DataCache: Skipping cache on server side for ${e}`),!1;let{ttl:s=r.ej.DEFAULT_TTL,useCompression:n=!1,storage:o="localStorage"}=a,i={data:t,timestamp:Date.now(),ttl:s,version:this.version},l=JSON.stringify(i);n&&this.compressionSupported&&(l=await this.compress(l));let c="localStorage"===o?localStorage:sessionStorage,d=this.getFullKey(e);return c.setItem(d,l),console.log(`💾 DataCache: Cached ${e} (${l.length} bytes)`),!0}catch(t){return console.error(`❌ DataCache: Failed to cache ${e}:`,t),!1}}async get(e,t={}){try{if(!this.isBrowser())return null;let{storage:a="localStorage"}=t,r="localStorage"===a?localStorage:sessionStorage,s=this.getFullKey(e),n=r.getItem(s);if(!n)return null;let o=n;this.compressionSupported&&this.isCompressed(n)&&(o=await this.decompress(n));let i=JSON.parse(o);if(i.version!==this.version)return console.warn(`⚠️ DataCache: Version mismatch for ${e}, removing`),this.remove(e,t),null;if(Date.now()-i.timestamp>i.ttl)return console.log(`⏰ DataCache: ${e} expired, removing`),this.remove(e,t),null;return console.log(`📖 DataCache: Retrieved ${e} from cache`),i.data}catch(a){return console.error(`❌ DataCache: Failed to retrieve ${e}:`,a),this.remove(e,t),null}}remove(e,t={}){try{if(!this.isBrowser())return;let{storage:a="localStorage"}=t,r="localStorage"===a?localStorage:sessionStorage,s=this.getFullKey(e);r.removeItem(s),console.log(`🗑️ DataCache: Removed ${e}`)}catch(t){console.error(`❌ DataCache: Failed to remove ${e}:`,t)}}clear(e="localStorage"){try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,a=[];for(let e=0;e<t.length;e++){let r=t.key(e);r&&r.startsWith("csv_market_dashboard_cache_")&&a.push(r)}a.forEach(e=>t.removeItem(e)),console.log(`🧹 DataCache: Cleared ${a.length} entries from ${e}`)}catch(t){console.error(`❌ DataCache: Failed to clear ${e}:`,t)}}getStats(e="localStorage"){if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,a=0,r=0,s=1/0,n=0;try{for(let e=0;e<t.length;e++){let o=t.key(e);if(o&&o.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(o);if(e){a++,r+=e.length;try{let t=JSON.parse(e);t.timestamp&&(s=Math.min(s,t.timestamp),n=Math.max(n,t.timestamp))}catch{}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:a,totalSize:r,oldestEntry:s===1/0?null:new Date(s),newestEntry:0===n?null:new Date(n)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},r.ej.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,a=[];for(let e=0;e<t.length;e++){let r=t.key(e);if(r&&r.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(r);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&a.push(r)}}catch{a.push(r)}}a.forEach(e=>t.removeItem(e)),a.length>0&&console.log(`🧹 DataCache: Cleaned up ${a.length} expired entries from ${e}`)})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return`csv_market_dashboard_cache_${e}`}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}}let n=s.getInstance(),o=r.ej.KEYS,i={cacheMarketData:async(e,t)=>n.set(e,t,{ttl:r.ej.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>n.set(e,t,{ttl:r.ej.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>n.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>n.get(e,{storage:"localStorage"})}},308:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(8570).createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\option-chain\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[276,314,432,54],()=>a(3215));module.exports=r})();