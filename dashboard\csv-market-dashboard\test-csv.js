// Simple CSV test script to verify the CSV file structure
const fs = require('fs');
const csv = require('csv-parser');

console.log('🧪 Testing CSV file structure...');

const results = [];
let count = 0;

fs.createReadStream('./instruments.csv')
  .pipe(csv())
  .on('data', (data) => {
    count++;
    if (count <= 5) {
      console.log(`Row ${count}:`, data);
    }
    
    // Check for NSE derivatives and index instruments
    if (data.EXCH_ID === 'NSE' && data.SEGMENT === 'D' && 
        ['FUTIDX', 'OPTIDX', 'INDEX'].includes(data.INSTRUMENT)) {
      results.push({
        SECURITY_ID: data.SECURITY_ID,
        SYMBOL_NAME: data.SYMBOL_NAME,
        INSTRUMENT: data.INSTRUMENT,
        EXCH_ID: data.EXCH_ID,
        SEGMENT: data.SEGMENT
      });
    }
  })
  .on('end', () => {
    console.log(`\n✅ Total rows processed: ${count}`);
    console.log(`🎯 NSE derivatives and index instruments found: ${results.length}`);
    
    if (results.length > 0) {
      console.log('\n📊 Sample NSE derivatives and index instruments:');
      results.slice(0, 10).forEach((item, index) => {
        console.log(`${index + 1}. ${item.SYMBOL_NAME} (${item.INSTRUMENT}) - ID: ${item.SECURITY_ID}`);
      });
      
      // Group by instrument type
      const breakdown = {};
      results.forEach(item => {
        breakdown[item.INSTRUMENT] = (breakdown[item.INSTRUMENT] || 0) + 1;
      });
      
      console.log('\n📈 Breakdown by instrument type:');
      Object.entries(breakdown).forEach(([type, count]) => {
        console.log(`   ${type}: ${count}`);
      });
      
      console.log('\n🎯 Security IDs to subscribe to:');
      const securityIds = results.map(item => item.SECURITY_ID);
      console.log(securityIds.slice(0, 20).join(', '), '...');
      console.log(`\nTotal Security IDs: ${securityIds.length}`);
    } else {
      console.log('❌ No matching instruments found. Check CSV file structure.');
    }
  })
  .on('error', (error) => {
    console.error('❌ Error reading CSV:', error);
  });
