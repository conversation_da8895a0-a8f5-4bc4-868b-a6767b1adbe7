/**
 * ✅ ENHANCED LOGGING SERVICE
 * Provides structured logging for market data debugging
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export enum LogCategory {
  SPOT = 'SPOT',
  DEPTH = 'DEPTH',
  ASK = 'ASK',
  BID = 'BID',
  STRIKE = 'STRIKE',
  EXPIRY = 'EXPIRY',
  SUBSCRIPTION = 'SUBSCRIPTION',
  CONNECTION = 'CONNECTION',
  PARSING = 'PARSING'
}

interface LogEntry {
  timestamp: number;
  level: LogLevel;
  category: LogCategory;
  message: string;
  data?: any;
}

class LoggingService {
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private currentLevel = LogLevel.INFO;
  private enabledCategories = new Set<LogCategory>(Object.values(LogCategory));

  /**
   * Set minimum log level
   */
  setLevel(level: LogLevel): void {
    this.currentLevel = level;
  }

  /**
   * Enable/disable specific log categories
   */
  setCategories(categories: LogCategory[]): void {
    this.enabledCategories = new Set(categories);
  }

  /**
   * Log debug message
   */
  debug(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.DEBUG, category, message, data);
  }

  /**
   * Log info message
   */
  info(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.INFO, category, message, data);
  }

  /**
   * Log warning message
   */
  warn(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.WARN, category, message, data);
  }

  /**
   * Log error message
   */
  error(category: LogCategory, message: string, data?: any): void {
    this.log(LogLevel.ERROR, category, message, data);
  }

  /**
   * Internal logging method
   */
  private log(level: LogLevel, category: LogCategory, message: string, data?: any): void {
    if (level < this.currentLevel || !this.enabledCategories.has(category)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      category,
      message,
      data
    };

    this.logs.push(entry);

    // Trim logs if exceeding max
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with structured format
    const levelName = LogLevel[level];
    const prefix = `[${category}][${levelName}]`;
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(`${timestamp} ${prefix} ${message}`, data || '');
        break;
      case LogLevel.INFO:
        console.info(`${timestamp} ${prefix} ${message}`, data || '');
        break;
      case LogLevel.WARN:
        console.warn(`${timestamp} ${prefix} ${message}`, data || '');
        break;
      case LogLevel.ERROR:
        console.error(`${timestamp} ${prefix} ${message}`, data || '');
        break;
    }
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count = 100): LogEntry[] {
    return this.logs.slice(-count);
  }

  /**
   * Get logs by category
   */
  getLogsByCategory(category: LogCategory, count = 50): LogEntry[] {
    return this.logs
      .filter(log => log.category === category)
      .slice(-count);
  }

  /**
   * Clear all logs
   */
  clear(): void {
    this.logs = [];
  }

  /**
   * Export logs as JSON
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * Log market data packet details
   */
  logMarketDataPacket(
    symbol: string,
    responseCode: number,
    bufferLength: number,
    parsedData: any
  ): void {
    const packetTypes: { [key: number]: string } = {
      2: 'TICKER',
      4: 'QUOTE',
      5: 'OI',
      6: 'CLOSE',
      8: 'FULL'
    };

    const packetType = packetTypes[responseCode] || `UNKNOWN_${responseCode}`;
    
    this.debug(LogCategory.PARSING, `${symbol} ${packetType} packet`, {
      responseCode,
      bufferLength,
      ltp: parsedData.ltp,
      volume: parsedData.volume,
      openInterest: parsedData.openInterest,
      bid: parsedData.bid,
      ask: parsedData.ask
    });
  }

  /**
   * Log market depth details
   */
  logMarketDepth(symbol: string, marketDepth: any[]): void {
    if (!marketDepth || marketDepth.length === 0) {
      this.warn(LogCategory.DEPTH, `${symbol} No market depth data`);
      return;
    }

    this.debug(LogCategory.DEPTH, `${symbol} Market depth (${marketDepth.length} levels)`, {
      bestBid: marketDepth[0]?.bidPrice,
      bestAsk: marketDepth[0]?.askPrice,
      bidQty: marketDepth[0]?.bidQty,
      askQty: marketDepth[0]?.askQty,
      allLevels: marketDepth
    });
  }

  /**
   * Log NIFTY spot price updates
   */
  logNiftySpotUpdate(price: number, source: string, securityId?: string): void {
    this.info(LogCategory.SPOT, `NIFTY Spot: ₹${price} from ${source}`, {
      price,
      source,
      securityId,
      timestamp: Date.now()
    });
  }

  /**
   * Log subscription details
   */
  logSubscription(instruments: any[], batchSize: number): void {
    const breakdown = instruments.reduce((acc, inst) => {
      acc[inst.instrumentType] = (acc[inst.instrumentType] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    this.info(LogCategory.SUBSCRIPTION, `Subscribing to ${instruments.length} instruments`, {
      total: instruments.length,
      batchSize,
      breakdown,
      sample: instruments.slice(0, 3).map(i => ({
        symbol: i.symbol,
        exchange: i.exchange,
        securityId: i.securityId
      }))
    });
  }

  /**
   * Log connection events
   */
  logConnection(event: string, details?: any): void {
    this.info(LogCategory.CONNECTION, event, details);
  }

  /**
   * Log option chain building
   */
  logOptionChainBuild(expiry: string, spotPrice: number, strikeCount: number): void {
    this.info(LogCategory.STRIKE, `Building option chain for ${expiry}`, {
      expiry,
      spotPrice,
      strikeCount,
      timestamp: Date.now()
    });
  }
}

// Export singleton instance
export const loggingService = new LoggingService();

// Set default configuration for development
if (process.env.NODE_ENV === 'development') {
  loggingService.setLevel(LogLevel.DEBUG);
} else {
  loggingService.setLevel(LogLevel.INFO);
}
