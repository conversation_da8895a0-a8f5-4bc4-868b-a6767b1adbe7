/**
 * WebSocket Context Provider for Shared Connection Management
 * Ensures single WebSocket connection across the entire application
 */

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import WebSocketManager from '@/lib/websocket-manager';

interface MarketData {
  securityId: string;
  symbol: string;
  exchange: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  high?: number;
  low?: number;
  open?: number;
  close?: number;
  timestamp: number;
  bid?: number;
  ask?: number;
  bidQty?: number;
  askQty?: number;
  openInterest?: number;
  previousOI?: number;
}

interface WebSocketContextType {
  marketData: MarketData[];
  isConnected: boolean;
  error: string | null;
  lastUpdate: Date | null;
  totalInstruments: number;
  isLoading: boolean;
  cacheLoaded: boolean;
  connectionStats: any;
  refreshFromCache: () => Promise<void>;
  clearCache: () => void;
  getMarketData: (securityId: string) => MarketData | undefined;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

interface WebSocketProviderProps {
  children: ReactNode;
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [cacheLoaded, setCacheLoaded] = useState(false);
  const [connectionStats, setConnectionStats] = useState<any>({});
  const [wsManager] = useState(() => WebSocketManager.getInstance());

  // Initialize cache and load cached data on mount
  useEffect(() => {
    const loadCachedData = async () => {
      try {
        console.log('📖 WebSocketProvider: Loading cached market data...');

        // First, try to load from localStorage for instant display
        const localData = localStorage.getItem('marketData');
        const localTimestamp = localStorage.getItem('marketDataTimestamp');

        if (localData && localTimestamp) {
          try {
            const parsedData = JSON.parse(localData);
            const timestamp = new Date(localTimestamp);
            const now = new Date();
            const ageMinutes = (now.getTime() - timestamp.getTime()) / (1000 * 60);

            // Use localStorage data if it's less than 5 minutes old
            if (ageMinutes < 5 && Array.isArray(parsedData) && parsedData.length > 0) {
              setMarketData(parsedData);
              setLastUpdate(timestamp);
              console.log(`⚡ WebSocketProvider: Loaded ${parsedData.length} items from localStorage (${ageMinutes.toFixed(1)}min old)`);
            }
          } catch (e) {
            console.warn('⚠️ WebSocketProvider: Failed to parse localStorage data:', e);
            localStorage.removeItem('marketData');
            localStorage.removeItem('marketDataTimestamp');
          }
        }

        // Then load fresh data from Redis via API
        const response = await fetch('/api/cache/bulk');
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
            setMarketData(result.data);
            const now = new Date();
            setLastUpdate(now);

            // Save to localStorage for next time
            localStorage.setItem('marketData', JSON.stringify(result.data));
            localStorage.setItem('marketDataTimestamp', now.toISOString());

            console.log(`✅ WebSocketProvider: Loaded ${result.data.length} items from Redis cache`);
          } else {
            console.log('📭 WebSocketProvider: No cached data found in Redis');
          }
        } else {
          console.log('📭 WebSocketProvider: Failed to load cached data from Redis');
        }

        setCacheLoaded(true);
      } catch (error) {
        console.error('❌ WebSocketProvider: Failed to load cached data:', error);
        setCacheLoaded(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadCachedData();
  }, []);

  // Initialize WebSocket connection
  useEffect(() => {
    let mounted = true;

    const initializeConnection = async () => {
      try {
        console.log('🔌 WebSocketProvider: Initializing WebSocket connection...');
        
        await wsManager.connect({
          onConnect: () => {
            if (mounted) {
              console.log('✅ WebSocketProvider: Connected to WebSocket');
              setIsConnected(true);
              setError(null);
              setIsLoading(false);
            }
          },
          onDisconnect: (reason) => {
            if (mounted) {
              console.log('❌ WebSocketProvider: Disconnected from WebSocket:', reason);
              setIsConnected(false);
              setError(`Disconnected: ${reason}`);
            }
          },
          onError: (err) => {
            if (mounted) {
              console.error('🔥 WebSocketProvider: WebSocket error:', err);
              setError(err.message);
              setIsLoading(false);
            }
          },
          onReconnect: (attemptNumber) => {
            if (mounted) {
              console.log(`🔄 WebSocketProvider: Reconnected after ${attemptNumber} attempts`);
              setIsConnected(true);
              setError(null);
            }
          },
          onMarketData: (data) => {
            if (mounted) {
              setMarketData(prev => {
                const newData = [...prev];
                const existingIndex = newData.findIndex(item => item.securityId === data.securityId);

                if (existingIndex >= 0) {
                  // Only update if the new data is more recent
                  const existing = newData[existingIndex];
                  if (data.timestamp >= existing.timestamp) {
                    newData[existingIndex] = data;
                  }
                } else {
                  newData.push(data);
                }

                return newData;
              });
              setLastUpdate(new Date());

              // Cache individual market data updates for NIFTY spot
              if (data.securityId === '13') {
                fetch('/api/cache/item', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ key: 'nifty_spot', data })
                }).catch(error => console.error('❌ Failed to cache NIFTY spot data:', error));
              }
            }
          },
          onMarketDataBatch: (dataArray) => {
            if (mounted) {
              setMarketData(prev => {
                const dataMap = new Map(prev.map(item => [item.securityId, item]));

                dataArray.forEach(data => {
                  // Only update if the new data is more recent or if we don't have this security
                  const existing = dataMap.get(data.securityId);
                  if (!existing || data.timestamp >= existing.timestamp) {
                    dataMap.set(data.securityId, data);
                  }
                });

                const newData = Array.from(dataMap.values());

                // Cache the batch data periodically (not on every update to avoid performance issues)
                if (dataArray.length > 10) { // Only cache for significant batch updates
                  fetch('/api/cache/bulk', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(newData)
                  }).catch(error => console.error('❌ Failed to cache bulk data:', error));
                }

                return newData;
              });
              setLastUpdate(new Date());
            }
          }
        });

        // Get initial connection stats
        const stats = wsManager.getStats();
        setConnectionStats(stats);
        
      } catch (err) {
        if (mounted) {
          console.error('❌ WebSocketProvider: Failed to initialize connection:', err);
          setError(err instanceof Error ? err.message : 'Connection failed');
          setIsLoading(false);
        }
      }
    };

    initializeConnection();

    // Cleanup on unmount
    return () => {
      mounted = false;
      console.log('🧹 WebSocketProvider: Cleaning up...');
      // Note: Don't disconnect here as other components might still need the connection
      // The WebSocketManager will handle cleanup when all clients are removed
    };
  }, [wsManager]);

  // Cache market data periodically
  useEffect(() => {
    if (marketData.length > 0) {
      const interval = setInterval(() => {
        fetch('/api/cache/bulk', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(marketData)
        }).catch(error => console.error('❌ Failed to cache periodic data:', error));
      }, 30000); // Cache every 30 seconds

      return () => clearInterval(interval);
    }
  }, [marketData]);

  // Update connection stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const stats = wsManager.getStats();
      setConnectionStats(stats);
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [wsManager]);

  const refreshFromCache = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/cache/bulk');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
          setMarketData(result.data);
          setLastUpdate(new Date());
          console.log(`✅ WebSocketProvider: Refreshed ${result.data.length} items from cache`);
        } else {
          console.log('📭 WebSocketProvider: No cached data found');
        }
      } else {
        console.log('📭 WebSocketProvider: Failed to refresh from cache');
      }
    } catch (error) {
      console.error('❌ WebSocketProvider: Failed to refresh from cache:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearCacheData = async () => {
    try {
      const response = await fetch('/api/cache/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pattern: 'market_data:*' })
      });

      if (response.ok) {
        setMarketData([]);
        setLastUpdate(null);
        console.log('🧹 WebSocketProvider: Cache cleared');
      } else {
        console.error('❌ WebSocketProvider: Failed to clear cache');
      }
    } catch (error) {
      console.error('❌ WebSocketProvider: Failed to clear cache:', error);
    }
  };

  const getMarketData = (securityId: string): MarketData | undefined => {
    return marketData.find(item => item.securityId === securityId);
  };

  const contextValue: WebSocketContextType = {
    marketData,
    isConnected,
    error,
    lastUpdate,
    totalInstruments: marketData.length,
    isLoading,
    cacheLoaded,
    connectionStats,
    refreshFromCache,
    clearCache: clearCacheData,
    getMarketData,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
    </WebSocketContext.Provider>
  );
}

export function useWebSocketContext(): WebSocketContextType {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider');
  }
  return context;
}

// Backward compatibility - export the hook with the old name
export const useMarketData = useWebSocketContext;
