# Enhanced State Management Migration Guide

## Overview

This migration transforms your CSV Market Dashboard from basic state management to a comprehensive, production-ready system with:

✅ **Unified State Management** - Single Zustand store replacing multiple contexts  
✅ **Real-time WebSocket Integration** - Enhanced connection management  
✅ **Persistent State** - Data survives page refreshes  
✅ **Optimized Performance** - Reduced re-renders and memory usage  
✅ **Error Recovery** - Automatic reconnection and error handling  
✅ **Enhanced Caching** - Intelligent Redis integration  

## Key Improvements

### 1. State Management Architecture

**Before:**
- Multiple state systems (Zustand + React Context)
- Inefficient re-renders
- Memory leaks
- No persistence across refreshes

**After:**
- Single enhanced Zustand store
- Optimized selectors for minimal re-renders
- Automatic state persistence
- Memory leak prevention

### 2. WebSocket Connection Management

**Before:**
- Basic connection handling
- Manual reconnection
- Connection state inconsistencies

**After:**
- Intelligent connection pooling
- Automatic reconnection with exponential backoff
- Connection statistics and monitoring
- Graceful error recovery

### 3. Data Persistence

**Before:**
- Data lost on page refresh
- Basic localStorage usage
- No cache synchronization

**After:**
- Multi-layer caching (localStorage + Redis)
- Automatic state restoration
- Cache invalidation strategies
- Offline support

### 4. Performance Optimizations

**Before:**
- Full component re-renders
- Inefficient data structures
- Memory accumulation

**After:**
- Selective re-rendering with optimized selectors
- Map-based data structures for O(1) lookups
- Automatic garbage collection
- Performance monitoring

## Migration Steps

### Step 1: Install Dependencies

```bash
npm install react-error-boundary@^4.0.13 react-hot-toast@^2.4.1
# or
yarn add react-error-boundary@^4.0.13 react-hot-toast@^2.4.1
```

### Step 2: Update Your Components

**Before:**
```typescript
import { useMarketData } from '@/hooks/useMarketData';
import { useWebSocketContext } from '@/contexts/WebSocketContext';

const MyComponent = () => {
  const { marketData, isConnected } = useMarketData();
  const { error } = useWebSocketContext();
  // ...
};
```

**After:**
```typescript
import { useEnhancedMarketData } from '@/hooks/useEnhancedMarketData';

const MyComponent = () => {
  const { 
    marketData, 
    isConnected, 
    connectionError,
    stats,
    updateFilters,
    getDataBySecurityId 
  } = useEnhancedMarketData();
  // ...
};
```

### Step 3: Update State Access Patterns

**Before:**
```typescript
// Inefficient: causes re-render on any marketData change
const allData = useMarketStore(state => state.marketData);
const specificItem = allData.find(item => item.securityId === '123');
```

**After:**
```typescript
// Optimized: only re-renders when specific item changes
const getDataBySecurityId = useEnhancedMarketStore(state => state.getMarketDataBySecurityId);
const specificItem = getDataBySecurityId('123');
```

### Step 4: Replace Context Usage

**Remove:**
```typescript
// Delete these imports and usage
import { WebSocketProvider } from '@/contexts/WebSocketContext';
import { useWebSocketContext } from '@/contexts/WebSocketContext';
```

**Replace with:**
```typescript
// Use the enhanced hook everywhere
import { useEnhancedMarketData } from '@/hooks/useEnhancedMarketData';
```

### Step 5: Update Error Handling

**Before:**
```typescript
if (error) {
  return <div>Error: {error}</div>;
}
```

**After:**
```typescript
if (connectionError) {
  return (
    <div className="error-container">
      <span>Connection Error: {connectionError}</span>
      <button onClick={reconnect}>Retry</button>
    </div>
  );
}
```

## New Features Available

### 1. Advanced Filtering and Sorting

```typescript
const { updateFilters, filters, getSortedData } = useEnhancedMarketData();

// Apply filters
updateFilters({
  exchange: ['NSE', 'BSE'],
  priceRange: [100, 1000],
  volumeThreshold: 10000
});

// Get sorted data
const sortedData = getSortedData();
```

### 2. Subscription Management

```typescript
const { subscribe, unsubscribe } = useEnhancedMarketData();

// Subscribe to specific instruments
subscribe('123456');
unsubscribe('123456');
```

### 3. Connection Statistics

```typescript
const { stats, connectionStats } = useEnhancedMarketData();

console.log('Total messages:', stats.messagesReceived);
console.log('Connection uptime:', stats.connectionUptime);
console.log('Cache size:', stats.cacheSize);
```

### 4. Manual Cache Control

```typescript
const { refresh, save, clearCache } = useEnhancedMarketData();

// Force refresh from cache
await refresh();

// Force save to cache
await save();

// Clear all cached data
await clearCache();
```

## Configuration Options

The enhanced hook accepts configuration options:

```typescript
const marketData = useEnhancedMarketData({
  autoConnect: true,           // Automatically connect WebSocket
  autoLoadCache: true,         // Load cached data on mount
  autoSaveInterval: 30000,     // Auto-save every 30 seconds
  reconnectOnError: true,      // Auto-reconnect on errors
  maxReconnectAttempts: 5,     // Maximum reconnection attempts
});
```

## Performance Best Practices

### 1. Use Specific Selectors

```typescript
// ❌ BAD: Causes re-render on any change
const entireState = useEnhancedMarketStore();

// ✅ GOOD: Only re-renders when connection status changes
const isConnected = useEnhancedMarketStore(state => state.connection.isConnected);
```

### 2. Optimize Component Structure

```typescript
// ❌ BAD: Single component handles everything
const Dashboard = () => {
  const { marketData, filters, stats } = useEnhancedMarketData();
  return (
    <div>
      <StatsPanel stats={stats} />
      <FilterPanel filters={filters} />
      <DataTable data={marketData} />
    </div>
  );
};

// ✅ GOOD: Separate components with specific subscriptions
const Dashboard = () => (
  <div>
    <StatsPanel />
    <FilterPanel />
    <DataTable />
  </div>
);

const StatsPanel = () => {
  const stats = useEnhancedMarketStore(selectConnectionStats);
  return <div>{/* stats display */}</div>;
};
```

### 3. Use Memoization for Expensive Operations

```typescript
const ExpensiveComponent = () => {
  const getSortedData = useEnhancedMarketStore(state => state.getSortedData);
  
  // Memoize expensive calculations
  const processedData = useMemo(() => {
    const sorted = getSortedData();
    return sorted.map(item => ({
      ...item,
      calculatedField: expensiveCalculation(item)
    }));
  }, [getSortedData]);
  
  return <div>{/* render processedData */}</div>;
};
```

## Troubleshooting

### Connection Issues

If you experience connection problems:

1. Check the WebSocket URL in your environment variables
2. Verify Redis configuration
3. Monitor connection stats:

```typescript
const { connectionStats, reconnect } = useEnhancedMarketData();

console.log('Connection attempts:', connectionStats.reconnectAttempts);
if (connectionStats.reconnectAttempts > 3) {
  // Force reconnect
  await reconnect();
}
```

### Performance Issues

If you notice performance problems:

1. Use React DevTools Profiler to identify unnecessary re-renders
2. Ensure you're using specific selectors
3. Check for memory leaks in the browser's Performance tab

### Cache Issues

If cached data seems stale:

```typescript
const { refresh, clearCache } = useEnhancedMarketData();

// Clear and reload
await clearCache();
await refresh();
```

## Rollback Plan

If you need to rollback to the previous system:

1. Restore the old hooks and contexts
2. Update component imports
3. Remove enhanced state management files
4. Update package.json dependencies

Keep the old files backed up during migration for easy rollback.

## Support

For issues or questions about the enhanced state management system:

1. Check the console for detailed error messages
2. Enable debug mode by setting `localStorage.setItem('debug', 'true')`
3. Review the connection statistics for insights
4. Use the built-in error recovery mechanisms

The enhanced system provides much better debugging information and self-healing capabilities compared to the previous implementation.
