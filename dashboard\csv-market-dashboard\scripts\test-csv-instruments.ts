
// ============================================================================
// CSV INSTRUMENTS TEST SCRIPT - Comprehensive testing of CSV functionality
// ============================================================================

import { CSVService } from '../src/services/CSVService';
import { MarketDataService } from '../src/services/MarketDataService';
import { InstrumentFilter } from '../src/types';

async function testCSVInstruments() {
  console.log('🧪 CSV Instruments Test Suite');
  console.log('=' .repeat(60));

  try {
    // Test 1: Initialize CSV Service
    console.log('\n📋 Test 1: Initialize CSV Service');
    const csvService = new CSVService('./instruments.csv');
    console.log('✅ CSV Service initialized');

    // Test 2: Load instruments
    console.log('\n📊 Test 2: Load Instruments');
    const startTime = Date.now();
    const instruments = await csvService.loadInstruments();
    const loadTime = Date.now() - startTime;
    
    console.log(`✅ Loaded ${instruments.length} instruments in ${loadTime}ms`);
    console.log(`⚡ Performance: ${Math.round(instruments.length / (loadTime / 1000))} instruments/second`);

    if (instruments.length === 0) {
      throw new Error('No instruments loaded - check CSV file');
    }

    // Test 3: Validate instrument structure
    console.log('\n🔍 Test 3: Validate Instrument Structure');
    const sampleInstrument = instruments[0];
    const requiredFields = ['securityId', 'symbol', 'displayName', 'exchange', 'instrumentType'];
    
    for (const field of requiredFields) {
      if (!(field in sampleInstrument)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    console.log('✅ Instrument structure validation passed');

    // Test 4: Test exchanges
    console.log('\n🏢 Test 4: Test Exchanges');
    const exchanges = await csvService.getExchanges();
    console.log(`✅ Found ${exchanges.length} exchanges: ${exchanges.join(', ')}`);

    // Test 5: Test instrument types
    console.log('\n📈 Test 5: Test Instrument Types');
    const instrumentTypes = await csvService.getInstrumentTypes();
    console.log(`✅ Found ${instrumentTypes.length} instrument types: ${instrumentTypes.join(', ')}`);

    // Test 6: Test filtering
    console.log('\n🔧 Test 6: Test Filtering');
    
    // Filter by NSE equity
    const nseEquityFilter: InstrumentFilter = {
      exchange: ['NSE_EQ'],
      instrumentType: ['EQUITY']
    };
    const nseEquity = await csvService.getInstruments(nseEquityFilter);
    console.log(`✅ NSE Equity filter: ${nseEquity.total} instruments`);

    // Filter by derivatives
    const derivativesFilter: InstrumentFilter = {
      instrumentType: ['FUTCUR', 'OPTCUR', 'FUTSTK', 'OPTSTK']
    };
    const derivatives = await csvService.getInstruments(derivativesFilter);
    console.log(`✅ Derivatives filter: ${derivatives.total} instruments`);

    // Test 7: Test search functionality
    console.log('\n🔎 Test 7: Test Search Functionality');
    
    const searchQueries = ['USDINR', 'NIFTY', 'RELIANCE', 'TCS'];
    for (const query of searchQueries) {
      const results = await csvService.searchInstruments(query, 5);
      console.log(`✅ Search "${query}": ${results.length} results`);
      if (results.length > 0) {
        console.log(`   First result: ${results[0].symbol} - ${results[0].displayName}`);
      }
    }

    // Test 8: Test specific instrument lookup
    console.log('\n🎯 Test 8: Test Specific Instrument Lookup');
    const firstInstrument = instruments[0];
    const found = await csvService.getInstrumentById(firstInstrument.securityId);
    if (found && found.securityId === firstInstrument.securityId) {
      console.log(`✅ Instrument lookup successful: ${found.symbol}`);
    } else {
      throw new Error('Instrument lookup failed');
    }

    // Test 9: Test market data service initialization
    console.log('\n📡 Test 9: Test Market Data Service');
    const marketDataService = new MarketDataService();
    console.log('✅ Market Data Service initialized');

    // Test 10: Performance and memory tests
    console.log('\n⚡ Test 10: Performance and Memory Tests');
    const memoryUsage = process.memoryUsage();
    console.log(`✅ Memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);
    console.log(`✅ RSS: ${Math.round(memoryUsage.rss / 1024 / 1024)}MB`);

    // Test 11: Data quality checks
    console.log('\n🔍 Test 11: Data Quality Checks');
    
    let validInstruments = 0;
    let invalidInstruments = 0;
    
    for (const instrument of instruments.slice(0, 1000)) { // Check first 1000 for performance
      if (instrument.securityId && instrument.symbol && instrument.exchange) {
        validInstruments++;
      } else {
        invalidInstruments++;
      }
    }
    
    console.log(`✅ Valid instruments: ${validInstruments}`);
    console.log(`⚠️ Invalid instruments: ${invalidInstruments}`);
    
    const qualityScore = (validInstruments / (validInstruments + invalidInstruments)) * 100;
    console.log(`📊 Data quality score: ${qualityScore.toFixed(2)}%`);

    // Test 12: Exchange distribution
    console.log('\n📊 Test 12: Exchange Distribution');
    const exchangeStats: Record<string, number> = {};
    instruments.forEach(inst => {
      exchangeStats[inst.exchange] = (exchangeStats[inst.exchange] || 0) + 1;
    });
    
    Object.entries(exchangeStats).forEach(([exchange, count]) => {
      const percentage = ((count / instruments.length) * 100).toFixed(1);
      console.log(`   ${exchange}: ${count.toLocaleString()} (${percentage}%)`);
    });

    // Test 13: Instrument type distribution
    console.log('\n📈 Test 13: Instrument Type Distribution');
    const typeStats: Record<string, number> = {};
    instruments.forEach(inst => {
      typeStats[inst.instrumentType] = (typeStats[inst.instrumentType] || 0) + 1;
    });
    
    Object.entries(typeStats).forEach(([type, count]) => {
      const percentage = ((count / instruments.length) * 100).toFixed(1);
      console.log(`   ${type}: ${count.toLocaleString()} (${percentage}%)`);
    });

    // Final summary
    console.log('\n🎉 Test Summary');
    console.log('=' .repeat(60));
    console.log(`✅ All tests passed successfully!`);
    console.log(`📊 Total instruments: ${instruments.length.toLocaleString()}`);
    console.log(`🏢 Exchanges: ${exchanges.length}`);
    console.log(`📈 Instrument types: ${instrumentTypes.length}`);
    console.log(`⚡ Load time: ${loadTime}ms`);
    console.log(`💾 Memory usage: ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`);
    console.log(`🎯 Data quality: ${qualityScore.toFixed(2)}%`);

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testCSVInstruments().catch(console.error);
}
