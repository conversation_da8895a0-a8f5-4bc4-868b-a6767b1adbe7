exports.id=54,exports.ids=[54],exports.modules={6804:()=>{},8645:()=>{},5961:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2994,23)),Promise.resolve().then(n.t.bind(n,6114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,9671,23)),Promise.resolve().then(n.t.bind(n,1868,23)),Promise.resolve().then(n.t.bind(n,4759,23))},7134:(e,t,n)=>{Promise.resolve().then(n.bind(n,7480))},7480:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l});var s=n(326),o=n(6465),i=n.n(o),c=n(381),a=n(938);function r({error:e,resetErrorBoundary:t}){return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[s.jsx("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3",children:s.jsx("span",{className:"text-red-600 text-lg",children:"⚠"})}),s.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Application Error"})]}),s.jsx("p",{className:"text-gray-600 mb-4",children:"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."}),(0,s.jsxs)("details",{className:"mb-4",children:[s.jsx("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"Technical Details"}),s.jsx("pre",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32",children:e.message})]}),(0,s.jsxs)("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:t,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Try Again"}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"Refresh Page"})]})]})})}function l({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[(0,s.jsxs)("head",{children:[s.jsx("title",{children:"CSV Market Dashboard"}),s.jsx("meta",{name:"description",content:"Real-time market data dashboard with enhanced WebSocket management and data caching"}),s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),s.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),s.jsx("body",{className:i().className,children:s.jsx(a.SV,{FallbackComponent:r,onError:(e,t)=>{console.error("Application Error:",e,t)},onReset:()=>{localStorage.removeItem("enhanced-market-data"),localStorage.removeItem("enhanced-market-timestamp"),sessionStorage.clear()},children:(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20",children:[e,s.jsx(c.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})})})]})}n(3824)},3566:(e,t,n)=>{"use strict";n.d(t,{Hh:()=>o,Hz:()=>s,ej:()=>i});let s={BASE_URL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},o={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},i={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:52428800,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(process.env.PORT||"8080"),parseInt(process.env.NEXT_PORT||"3000"),process.env.LOG_LEVEL,process.env.ENABLE_METRICS},1223:(e,t,n)=>{"use strict";n.d(t,{A1:()=>s});let s={price:e=>!e||e<=0?"-":`₹${e.toFixed(2)}`,number:e=>null==e?"-":e.toLocaleString(),percentage:e=>e&&0!==e?`${e>=0?"+":""}${e.toFixed(2)}%`:"-",change:(e,t)=>{if(!e||0===e)return"-";let n=e>0?"+":"",s=t?` (${n}${t.toFixed(2)}%)`:"";return`${n}${e.toFixed(2)}${s}`},volume:e=>null==e?"-":function(e){return e>=1e7?`${(e/1e7).toFixed(2)} Cr`:e>=1e5?`${(e/1e5).toFixed(2)} L`:e>=1e3?`${(e/1e3).toFixed(2)} K`:e.toString()}(e),time:e=>e?new Date(e).toLocaleTimeString():"-",date:e=>e?new Date(e).toLocaleDateString():"-",bidAsk:(e,t)=>{if(!e||e<=0)return"-";let n=t?` (${t})`:"";return`₹${e.toFixed(2)}${n}`}}},3687:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>c});var s=n(7432),o=n(3566);class i{static{this.instance=null}static getInstance(){return i.instance||(i.instance=new i,console.log("\uD83D\uDD27 WebSocketManager: New singleton instance created")),i.instance}constructor(){this.socket=null,this.isConnecting=!1,this.clientCount=0,this.listeners=new Map,this.clientCallbacks=new Map,this.stats={connected:!1,clients:0,reconnectAttempts:0,lastConnected:null,totalMessages:0,errors:0,connectionId:null},this.heartbeatInterval=null,this.cleanupInterval=null,this.reconnectTimeout=null,console.log("\uD83D\uDD27 WebSocketManager: Constructor called")}async connect(e={}){let t=this.generateClientId();if(console.log(`🔌 WebSocketManager: Client ${t} requesting connection`),this.clientCallbacks.set(t,e),this.socket&&this.socket.connected)return console.log(`✅ WebSocketManager: Reusing existing connection for client ${t}`),this.addClient(e,t),this.socket;if(this.isConnecting)return console.log(`⏳ WebSocketManager: Connection in progress, waiting for client ${t}`),new Promise((n,s)=>{let o=()=>{this.socket&&this.socket.connected?(console.log(`✅ WebSocketManager: Connection ready for waiting client ${t}`),this.addClient(e,t),n(this.socket)):this.isConnecting?setTimeout(o,100):(console.log(`❌ WebSocketManager: Connection failed for waiting client ${t}`),s(Error("Connection failed")))};o()});console.log(`🚀 WebSocketManager: Creating new connection for client ${t}`),this.isConnecting=!0;try{return await this.createConnection(e),this.addClient(e,t),this.socket}catch(e){throw this.isConnecting=!1,this.clientCallbacks.delete(t),console.error(`❌ WebSocketManager: Connection failed for client ${t}:`,e),e}}generateClientId(){return`client_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}async createConnection(e){return new Promise((e,t)=>{let n=o.Hz.BASE_URL;console.log(`🔌 WebSocketManager: Creating connection to ${n}`),this.socket&&(console.log("\uD83E\uDDF9 WebSocketManager: Cleaning up existing connection"),this.socket.removeAllListeners(),this.socket.disconnect()),this.socket=(0,s.io)(n,{transports:["websocket","polling"],upgrade:!0,rememberUpgrade:!1,timeout:o.Hh.CONNECTION_TIMEOUT,forceNew:!1,reconnection:!0,reconnectionAttempts:o.Hh.MAX_RECONNECT_ATTEMPTS,reconnectionDelay:o.Hh.RECONNECT_INTERVAL,reconnectionDelayMax:4*o.Hh.RECONNECT_INTERVAL,randomizationFactor:.5,autoConnect:!0}),this.socket.on("connect",()=>{console.log("✅ WebSocketManager: Connected successfully"),this.isConnecting=!1,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.reconnectAttempts=0,this.stats.connectionId=this.socket?.id||null,this.startHeartbeat(),this.notifyAllClients("onConnect"),e()}),this.socket.on("disconnect",e=>{console.log(`❌ WebSocketManager: Disconnected - ${e}`),this.stats.connected=!1,this.stats.connectionId=null,this.stopHeartbeat(),this.notifyAllClients("onDisconnect",e),"io client disconnect"!==e&&"client namespace disconnect"!==e&&this.scheduleReconnection()}),this.socket.on("connect_error",e=>{console.error("\uD83D\uDD25 WebSocketManager: Connection error:",e.message),this.stats.errors++,this.isConnecting=!1,this.notifyAllClients("onError",e),t(e)}),this.socket.on("reconnect",e=>{console.log(`🔄 WebSocketManager: Reconnected after ${e} attempts`),this.stats.reconnectAttempts=e,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.connectionId=this.socket?.id||null,this.notifyAllClients("onReconnect",e)}),this.socket.on("reconnect_attempt",e=>{console.log(`🔄 WebSocketManager: Reconnection attempt ${e}`),this.stats.reconnectAttempts=e}),this.socket.on("reconnect_failed",()=>{console.error("\uD83D\uDCA5 WebSocketManager: Reconnection failed"),this.stats.connected=!1,this.isConnecting=!1,this.stats.connectionId=null}),this.socket.on("marketData",e=>{this.stats.totalMessages++,this.notifyListeners("marketData",e)}),this.socket.on("marketDataBatch",e=>{this.stats.totalMessages+=e.length,this.notifyListeners("marketDataBatch",e)}),this.startCleanupInterval(),setTimeout(()=>{this.isConnecting&&(this.isConnecting=!1,t(Error("Connection timeout")))},o.Hh.CONNECTION_TIMEOUT)})}addClient(e,t){this.clientCount++,this.stats.clients=this.clientCount,e.onMarketData&&this.addListener("marketData",e.onMarketData),e.onMarketDataBatch&&this.addListener("marketDataBatch",e.onMarketDataBatch),console.log(`📊 WebSocketManager: Client ${t} added (Total: ${this.clientCount})`)}notifyAllClients(e,t){this.clientCallbacks.forEach((n,s)=>{try{switch(e){case"onConnect":n.onConnect?.();break;case"onDisconnect":n.onDisconnect?.(t);break;case"onError":n.onError?.(t);break;case"onReconnect":n.onReconnect?.(t)}}catch(e){console.error(`❌ WebSocketManager: Error notifying client ${s}:`,e)}})}scheduleReconnection(){this.reconnectTimeout&&clearTimeout(this.reconnectTimeout);let e=Math.min(o.Hh.RECONNECT_INTERVAL*Math.pow(2,this.stats.reconnectAttempts),3e4);console.log(`🔄 WebSocketManager: Scheduling reconnection in ${e}ms`),this.reconnectTimeout=setTimeout(()=>{!this.stats.connected&&this.clientCount>0&&(console.log("\uD83D\uDD04 WebSocketManager: Attempting auto-reconnection"),this.connect().catch(e=>{console.error("❌ WebSocketManager: Auto-reconnection failed:",e)}))},e)}removeClient(e){if(this.clientCount>0){this.clientCount--,this.stats.clients=this.clientCount;let t="unknown";for(let[n,s]of this.clientCallbacks.entries())if(s===e){this.clientCallbacks.delete(n),t=n;break}e.onMarketData&&this.removeListener("marketData",e.onMarketData),e.onMarketDataBatch&&this.removeListener("marketDataBatch",e.onMarketDataBatch),console.log(`📊 WebSocketManager: Client ${t} removed (Total: ${this.clientCount})`),0===this.clientCount&&(console.log("⏳ WebSocketManager: No clients remaining, scheduling disconnect"),setTimeout(()=>{0===this.clientCount&&(console.log("\uD83D\uDD0C WebSocketManager: Disconnecting due to no clients"),this.disconnect())},5e3))}}addListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set);let n=this.listeners.get(e);if(n.size>=o.Hh.MAX_LISTENERS_PER_EVENT){console.warn(`⚠️ Maximum listeners reached for event: ${e}`);return}n.add(t)}removeListener(e,t){let n=this.listeners.get(e);n&&(n.delete(t),0===n.size&&this.listeners.delete(e))}notifyListeners(e,t){let n=this.listeners.get(e);n&&n.forEach(n=>{try{n(t)}catch(t){console.error(`Error in ${e} listener:`,t)}})}startHeartbeat(){this.stopHeartbeat(),this.heartbeatInterval=setInterval(()=>{this.socket&&this.socket.connected&&this.socket.emit("ping")},o.Hh.HEARTBEAT_INTERVAL)}stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}startCleanupInterval(){this.cleanupInterval=setInterval(()=>{this.cleanup()},o.Hh.CLEANUP_INTERVAL)}cleanup(){let e=[];this.listeners.forEach((t,n)=>{0===t.size&&e.push(n)}),e.forEach(e=>{this.listeners.delete(e)}),console.log("\uD83D\uDCCA WebSocket Stats:",this.getStats())}disconnect(){console.log("\uD83D\uDD0C WebSocketManager: Disconnecting..."),this.stopHeartbeat(),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket&&(this.socket.removeAllListeners(),this.socket.disconnect(),this.socket=null),this.listeners.clear(),this.clientCallbacks.clear(),this.clientCount=0,this.stats.connected=!1,this.stats.clients=0,this.stats.connectionId=null,this.isConnecting=!1,console.log("✅ WebSocketManager: Disconnected and cleaned up")}getStats(){return{...this.stats}}isConnected(){return this.socket?.connected||!1}emit(e,t){this.socket&&this.socket.connected?this.socket.emit(e,t):console.warn(`Cannot emit ${e}: WebSocket not connected`)}}i.getInstance();let c=i},2029:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});let s=(0,n(8570).createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\layout.tsx#default`)},3824:()=>{}};