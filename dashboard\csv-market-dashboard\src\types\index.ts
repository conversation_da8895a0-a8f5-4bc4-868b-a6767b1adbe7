// ============================================================================
// CSV MARKET DASHBOARD TYPE DEFINITIONS
// ============================================================================

// CSV Instrument Data Types (based on the CSV structure)
export interface CSVInstrument {
  EXCH_ID: string;                    // Exchange ID (BSE, NSE, etc.)
  SEGMENT: string;                    // Market segment (C, F, O, etc.)
  SECURITY_ID: string;                // Unique security identifier
  ISIN: string;                       // ISIN number
  INSTRUMENT: string;                 // Instrument type (EQUITY, FUTCUR, OPTCUR, etc.)
  UNDERLYING_SECURITY_ID: string;     // Underlying security ID for derivatives
  UNDERLYING_SYMBOL: string;          // Underlying symbol
  SYMBOL_NAME: string;                // Trading symbol
  DISPLAY_NAME: string;               // Display name
  INSTRUMENT_TYPE: string;            // Instrument type category
  SERIES: string;                     // Series (EQ, BE, etc.)
  LOT_SIZE: string;                   // Lot size for trading
  SM_EXPIRY_DATE: string;             // Expiry date for derivatives
  STRIKE_PRICE: string;               // Strike price for options
  OPTION_TYPE: string;                // Option type (CE, PE, XX)
  TICK_SIZE: string;                  // Minimum tick size
  EXPIRY_FLAG: string;                // Expiry flag
  BRACKET_FLAG: string;               // Bracket order flag
  COVER_FLAG: string;                 // Cover order flag
  ASM_GSM_FLAG: string;               // ASM/GSM flag
  ASM_GSM_CATEGORY: string;           // ASM/GSM category
  BUY_SELL_INDICATOR: string;         // Buy/Sell indicator
  // Additional margin and range fields...
  MTF_LEVERAGE: string;               // MTF leverage
}

// Processed Instrument Data (normalized for application use)
export interface Instrument {
  securityId: string;
  symbol: string;
  displayName: string;
  exchange: string;
  segment: string;
  instrumentType: string;
  isin?: string;
  lotSize: number;
  tickSize: number;
  underlyingSymbol?: string;
  expiryDate?: Date;
  strikePrice?: number;
  optionType?: 'CE' | 'PE' | 'XX';
  isActive: boolean;
  exchangeCode: number;
}

// ✅ ENHANCED: Market depth level interface
export interface MarketDepthLevel {
  level: number;
  bidPrice: number;
  bidQty: number;
  askPrice: number;
  askQty: number;
}

// Market Data Types
export interface MarketData {
  securityId: string;
  symbol: string;
  exchange: string;
  ltp: number;                        // Last traded price
  change: number;                     // Price change
  changePercent: number;              // Percentage change
  volume: number;                     // Volume traded
  high: number;                       // Day high
  low: number;                        // Day low
  open: number;                       // Opening price
  close: number;                      // Previous close
  timestamp: number;                  // Update timestamp
  bid?: number;                       // Bid price
  ask?: number;                       // Ask price
  bidQty?: number;                    // Bid quantity
  askQty?: number;                    // Ask quantity
  openInterest?: number;              // Open Interest (for derivatives)
  // ✅ FIXED: Add expiry date for option matching
  expiryDate?: string;                // Expiry date in YYYY-MM-DD format (for derivatives)
  strikePrice?: number;               // Strike price (for options)
  optionType?: 'CE' | 'PE' | 'XX';    // Option type (for options)
  instrumentType?: string;            // Instrument type (EQUITY, OPTIDX, etc.)
  // ✅ NEW: Market depth data (5 levels of bid/ask)
  marketDepth?: MarketDepthLevel[];
  // ✅ NEW: Additional fields for better tracking
  previousClose?: number;
  ltt?: number;                       // Last trade time
  ltq?: number;                       // Last trade quantity
  atp?: number;                       // Average trade price
  totalBuyQuantity?: number;
  totalSellQuantity?: number;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'marketData' | 'error' | 'connection' | 'initialData';
  data?: any;
  error?: string;
  timestamp?: number;
}

export interface SubscriptionRequest {
  exchangeSegment: number;
  securityId: string;
}

// Filter and Search Types
export interface InstrumentFilter {
  exchange?: string[];                // Filter by exchanges
  instrumentType?: string[];          // Filter by instrument types
  segment?: string[];                 // Filter by segments
  search?: string;                    // Search term
  isActive?: boolean;                 // Active instruments only
  hasExpiry?: boolean;                // Instruments with expiry
  minLotSize?: number;                // Minimum lot size
  maxLotSize?: number;                // Maximum lot size
}

export interface SearchResult {
  instruments: Instrument[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
}

export interface InstrumentsResponse {
  instruments: Instrument[];
  total: number;
  exchanges: string[];
  instrumentTypes: string[];
  segments: string[];
}

export interface MarketDataResponse {
  connected: boolean;
  instruments: MarketData[];
  totalInstruments: number;
  activeSubscriptions: number;
  lastUpdate: number;
}

// Configuration Types
export interface CSVConfig {
  filePath: string;
  delimiter: string;
  encoding: string;
  skipEmptyLines: boolean;
  headers: boolean;
  cacheTTL: number;
}

export interface ServerConfig {
  port: number;
  host: string;
  cors: {
    origin: string[];
    credentials: boolean;
  };
  rateLimit: {
    windowMs: number;
    max: number;
  };
}

// Cache Types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  memoryUsage: number;
}

// Service Types
export interface CSVService {
  loadInstruments(): Promise<Instrument[]>;
  getInstruments(filter?: InstrumentFilter): Promise<SearchResult>;
  searchInstruments(query: string, limit?: number): Promise<Instrument[]>;
  getInstrumentById(securityId: string): Promise<Instrument | null>;
  getExchanges(): Promise<string[]>;
  getInstrumentTypes(): Promise<string[]>;
  refreshCache(): Promise<void>;
}

export interface IMarketDataService {
  connect(): Promise<void>;
  disconnect(): void;
  subscribe(instruments: Instrument[]): void;
  unsubscribe(instruments: Instrument[]): void;
  getMarketData(securityId: string): MarketData | null;
  getConnectionStatus(): boolean;
  connected: boolean;
}

// Error Types
export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  isOperational?: boolean;
}

// Component Props Types
export interface DashboardProps {
  initialInstruments?: Instrument[];
  autoConnect?: boolean;
  refreshInterval?: number;
}

export interface InstrumentTableProps {
  instruments: Instrument[];
  marketData: Map<string, MarketData>;
  onInstrumentSelect?: (instrument: Instrument) => void;
  loading?: boolean;
}

export interface FilterPanelProps {
  filter: InstrumentFilter;
  onFilterChange: (filter: InstrumentFilter) => void;
  exchanges: string[];
  instrumentTypes: string[];
  segments: string[];
}

// Utility Types
export type ExchangeSegment = 'NSE_EQ' | 'NSE_FNO' | 'BSE_EQ' | 'BSE_FNO' | 'MCX_COMM' | 'IDX_I';
export type InstrumentType = 'EQUITY' | 'FUTCUR' | 'OPTCUR' | 'FUTSTK' | 'OPTSTK' | 'INDEX';
export type MarketStatus = 'OPEN' | 'CLOSED' | 'PRE_OPEN' | 'POST_CLOSE';
export type DataSource = 'LIVE' | 'MOCK' | 'CACHED';

// Event Types
export interface InstrumentLoadEvent {
  type: 'INSTRUMENTS_LOADED';
  payload: {
    count: number;
    exchanges: string[];
    instrumentTypes: string[];
  };
}

export interface MarketDataEvent {
  type: 'MARKET_DATA_UPDATE';
  payload: MarketData;
}

export interface ConnectionEvent {
  type: 'CONNECTION_STATUS';
  payload: {
    connected: boolean;
    timestamp: number;
  };
}

export type AppEvent = InstrumentLoadEvent | MarketDataEvent | ConnectionEvent;

// State Management Types
export interface AppState {
  instruments: Instrument[];
  marketData: Map<string, MarketData>;
  filter: InstrumentFilter;
  connectionStatus: boolean;
  loading: boolean;
  error: string | null;
  lastUpdate: number;
}

export interface AppAction {
  type: string;
  payload?: any;
}

// Performance Monitoring Types
export interface PerformanceMetrics {
  csvParseTime: number;
  instrumentCount: number;
  memoryUsage: number;
  cacheHitRate: number;
  subscriptionCount: number;
  dataUpdateRate: number;
  timestamp: number;
}

// Option Chain Types
export interface OptionData {
  securityId: string;
  symbol: string;
  exchange: string;
  strikePrice: number;
  optionType: 'CE' | 'PE';
  expiryDate: string;
  ltp: number;
  change: number;
  changePercent: number;
  volume: number;
  openInterest?: number;
  bid?: number;
  ask?: number;
  bidQty?: number;
  askQty?: number;
  high?: number;
  low?: number;
  open?: number;
  close?: number;
  timestamp: number;
}

export interface OptionChainRow {
  strikePrice: number;
  call: OptionData | null;
  put: OptionData | null;
}

export interface OptionChainData {
  underlying: string;
  spotPrice: number;
  expiry: string;
  rows: OptionChainRow[];
  timestamp: number;
}

export interface ExpiryData {
  underlying: string;
  securityId: number;
  segment: string;
  expiries: string[];
  count: number;
  hasCredentials: boolean;
}
