/**
 * Enhanced Market Data Hook
 * Provides optimized access to market data with automatic initialization
 * Features:
 * - Automatic WebSocket connection management
 * - Optimized re-rendering with selective subscriptions
 * - Auto-save functionality
 * - Error recovery and reconnection
 */

import { useEffect, useCallback, useRef } from 'react';
// Temporarily disabled enhanced store import
// import { useEnhancedMarketStore, selectConnection, selectMarketData, selectCache, selectUI, selectIsLoading } from '@/store/enhancedMarketStore';

export interface UseEnhancedMarketDataOptions {
  autoConnect?: boolean;
  autoLoadCache?: boolean;
  autoSaveInterval?: number;
  reconnectOnError?: boolean;
  maxReconnectAttempts?: number;
}

export const useEnhancedMarketData = (options: UseEnhancedMarketDataOptions = {}) => {
  const {
    autoConnect = true,
    autoLoadCache = true,
    autoSaveInterval = 30000, // 30 seconds
    reconnectOnError = true,
    maxReconnectAttempts = 5,
  } = options;

  // Temporary fallback implementation
  const connection = {
    status: 'disconnected' as any,
    isConnected: false,
    error: null,
    connectionStats: {
      totalMessages: 0,
      connectionUptime: 0,
    },
  };
  const marketDataMap = new Map();
  const cache = {
    isLoaded: false,
    totalCacheSize: 0,
    pendingUpdates: 0,
    lastCacheUpdate: null,
  };
  const ui = {
    filters: {},
    sortConfig: { field: 'symbol', direction: 'asc' as any },
    selectedInstruments: new Set(),
    viewMode: 'table' as any,
    autoRefresh: true,
  };
  const isLoading = false;

  // Store actions - temporary mock functions with useCallback to fix dependency warnings
  const initializeConnection = useCallback(async () => {}, []);
  const connect = useCallback(async () => {}, []);
  const disconnect = useCallback(() => {}, []);
  const reconnect = useCallback(async () => {}, []);
  const loadFromCache = useCallback(async () => {}, []);
  const saveToCache = useCallback(async (force?: boolean) => {}, []);
  const clearCache = useCallback(async () => {}, []);
  const setFilters = useCallback((filters: any) => {}, []);
  const setSortConfig = useCallback((field: string, direction?: 'asc' | 'desc') => {}, []);
  const getFilteredMarketData = useCallback(() => [], []);
  const getSortedMarketData = useCallback(() => [], []);
  const getMarketDataBySecurityId = useCallback((securityId: string) => undefined, []);
  const getMarketDataBySymbol = useCallback((symbol: string) => undefined, []);
  const subscribeToInstrument = useCallback((securityId: string) => {}, []);
  const unsubscribeFromInstrument = useCallback((securityId: string) => {}, []);
  const reset = useCallback(() => {}, []);

  // Refs for intervals and tracking
  const autoSaveIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);
  const initializationRef = useRef(false);

  // Convert Map to Array for components that expect arrays
  const marketDataArray = Array.from(marketDataMap.values());

  // Initialize connection and cache on mount
  useEffect(() => {
    const initialize = async () => {
      if (initializationRef.current) return;
      initializationRef.current = true;

      console.log('🚀 Enhanced Hook: Initializing...');

      try {
        // Load cache first for instant data display
        if (autoLoadCache && !cache.isLoaded) {
          await loadFromCache();
        }

        // Initialize WebSocket connection
        if (autoConnect && connection.status === 'disconnected') {
          await initializeConnection();
        }
      } catch (error) {
        console.error('❌ Enhanced Hook: Initialization failed:', error);
      }
    };

    initialize();

    // Cleanup on unmount
    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, [autoConnect, autoLoadCache, cache.isLoaded, connection.status, initializeConnection, loadFromCache]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveInterval > 0 && marketDataArray.length > 0) {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }

      autoSaveIntervalRef.current = setInterval(() => {
        saveToCache();
      }, autoSaveInterval);

      return () => {
        if (autoSaveIntervalRef.current) {
          clearInterval(autoSaveIntervalRef.current);
        }
      };
    }
  }, [autoSaveInterval, marketDataArray.length, saveToCache]);

  // Auto-reconnect on error
  useEffect(() => {
    if (
      reconnectOnError &&
      connection.status === 'error' &&
      reconnectAttemptsRef.current < maxReconnectAttempts
    ) {
      const retryDelay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
      
      console.log(
        `🔄 Enhanced Hook: Auto-reconnecting in ${retryDelay}ms (attempt ${reconnectAttemptsRef.current + 1}/${maxReconnectAttempts})`
      );

      const timeoutId = setTimeout(async () => {
        try {
          reconnectAttemptsRef.current++;
          await reconnect();
        } catch (error) {
          console.error('❌ Enhanced Hook: Auto-reconnect failed:', error);
        }
      }, retryDelay);

      return () => clearTimeout(timeoutId);
    }
  }, [connection.status, reconnect, reconnectOnError, maxReconnectAttempts]);

  // Reset reconnect attempts on successful connection
  useEffect(() => {
    if (connection.status === 'connected') {
      reconnectAttemptsRef.current = 0;
    }
  }, [connection.status]);

  // Optimized data access functions
  const getFilteredData = useCallback(() => {
    return getFilteredMarketData();
  }, [getFilteredMarketData]);

  const getSortedData = useCallback(() => {
    return getSortedMarketData();
  }, [getSortedMarketData]);

  const getDataBySecurityId = useCallback((securityId: string) => {
    return getMarketDataBySecurityId(securityId);
  }, [getMarketDataBySecurityId]);

  const getDataBySymbol = useCallback((symbol: string) => {
    return getMarketDataBySymbol(symbol);
  }, [getMarketDataBySymbol]);

  // Enhanced filter functions
  const updateFilters = useCallback((filters: Partial<typeof ui.filters>) => {
    setFilters(filters);
  }, [setFilters]);

  const updateSort = useCallback((field: string, direction?: 'asc' | 'desc') => {
    setSortConfig(field, direction);
  }, [setSortConfig]);

  // Subscription management
  const subscribe = useCallback((securityId: string) => {
    subscribeToInstrument(securityId);
  }, [subscribeToInstrument]);

  const unsubscribe = useCallback((securityId: string) => {
    unsubscribeFromInstrument(securityId);
  }, [unsubscribeFromInstrument]);

  // Connection management
  const forceReconnect = useCallback(async () => {
    try {
      reconnectAttemptsRef.current = 0; // Reset attempts for manual reconnect
      await reconnect();
    } catch (error) {
      console.error('❌ Enhanced Hook: Manual reconnect failed:', error);
      throw error;
    }
  }, [reconnect]);

  const forceRefresh = useCallback(async () => {
    try {
      await loadFromCache();
    } catch (error) {
      console.error('❌ Enhanced Hook: Force refresh failed:', error);
      throw error;
    }
  }, [loadFromCache]);

  const forceSave = useCallback(async () => {
    try {
      await saveToCache(true);
    } catch (error) {
      console.error('❌ Enhanced Hook: Force save failed:', error);
      throw error;
    }
  }, [saveToCache]);

  // Stats and computed values
  const stats = {
    totalInstruments: marketDataArray.length,
    connectedInstruments: marketDataArray.filter(item => item.ltp && item.ltp > 0).length,
    lastUpdate: null, // Temporary mock
    cacheSize: cache.totalCacheSize,
    connectionUptime: connection.connectionStats.connectionUptime,
    messagesReceived: connection.connectionStats.totalMessages,
    reconnectAttempts: reconnectAttemptsRef.current,
    isAutoSaving: autoSaveIntervalRef.current !== null,
  };

  return {
    // Data
    marketData: marketDataArray,
    marketDataMap,
    filteredData: getFilteredData(),
    sortedData: getSortedData(),

    // Connection state
    isConnected: connection.isConnected,
    connectionStatus: connection.status,
    connectionError: connection.error,
    connectionStats: connection.connectionStats,

    // Cache state
    cacheLoaded: cache.isLoaded,
    cacheUpdating: cache.pendingUpdates > 0,
    lastCacheUpdate: cache.lastCacheUpdate,

    // UI state
    filters: ui.filters,
    sortConfig: ui.sortConfig,
    selectedInstruments: ui.selectedInstruments,
    viewMode: ui.viewMode,
    autoRefresh: ui.autoRefresh,

    // Loading states
    isLoading,
    isInitializing: !initializationRef.current,

    // Data access functions
    getDataBySecurityId,
    getDataBySymbol,
    getFilteredData,
    getSortedData,

    // Actions
    updateFilters,
    updateSort,
    subscribe,
    unsubscribe,

    // Connection management
    connect,
    disconnect,
    reconnect: forceReconnect,

    // Cache management
    refresh: forceRefresh,
    save: forceSave,
    clearCache,

    // Utility
    reset,
    stats,

    // Advanced actions (expose if needed)
    _store: {
      setFilters,
      setSortConfig,
      subscribeToInstrument,
      unsubscribeFromInstrument,
      // Add more if needed
    },
  };
};

export default useEnhancedMarketData;
