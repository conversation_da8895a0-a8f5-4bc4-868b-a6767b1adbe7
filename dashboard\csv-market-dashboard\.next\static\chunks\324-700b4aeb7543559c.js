(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[324],{257:function(t,e,s){"use strict";var r,i;t.exports=(null==(r=s.g.process)?void 0:r.env)&&"object"==typeof(null==(i=s.g.process)?void 0:i.env)?s.g.process:s(4227)},4227:function(t){!function(){var e={229:function(t){var e,s,r,i=t.exports={};function n(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}function a(t){if(e===setTimeout)return setTimeout(t,0);if((e===n||!e)&&setTimeout)return e=setTimeout,setTimeout(t,0);try{return e(t,0)}catch(s){try{return e.call(null,t,0)}catch(s){return e.call(this,t,0)}}}!function(){try{e="function"==typeof setTimeout?setTimeout:n}catch(t){e=n}try{s="function"==typeof clearTimeout?clearTimeout:o}catch(t){s=o}}();var h=[],c=!1,u=-1;function l(){c&&r&&(c=!1,r.length?h=r.concat(h):u=-1,h.length&&p())}function p(){if(!c){var t=a(l);c=!0;for(var e=h.length;e;){for(r=h,h=[];++u<e;)r&&r[u].run();u=-1,e=h.length}r=null,c=!1,function(t){if(s===clearTimeout)return clearTimeout(t);if((s===o||!s)&&clearTimeout)return s=clearTimeout,clearTimeout(t);try{s(t)}catch(e){try{return s.call(null,t)}catch(e){return s.call(this,t)}}}(t)}}function d(t,e){this.fun=t,this.array=e}function f(){}i.nextTick=function(t){var e=Array(arguments.length-1);if(arguments.length>1)for(var s=1;s<arguments.length;s++)e[s-1]=arguments[s];h.push(new d(t,e)),1!==h.length||c||a(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=f,i.addListener=f,i.once=f,i.off=f,i.removeListener=f,i.removeAllListeners=f,i.emit=f,i.prependListener=f,i.prependOnceListener=f,i.listeners=function(t){return[]},i.binding=function(t){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},s={};function r(t){var i=s[t];if(void 0!==i)return i.exports;var n=s[t]={exports:{}},o=!0;try{e[t](n,n.exports,r),o=!1}finally{o&&delete s[t]}return n.exports}r.ab="//";var i=r(229);t.exports=i}()},8680:function(t,e,s){"use strict";let r,i;s.d(e,{io:function(){return tO}});var n,o,a={};s.r(a),s.d(a,{Decoder:function(){return t_},Encoder:function(){return tg},PacketType:function(){return o},protocol:function(){return ty}});let h=Object.create(null);h.open="0",h.close="1",h.ping="2",h.pong="3",h.message="4",h.upgrade="5",h.noop="6";let c=Object.create(null);Object.keys(h).forEach(t=>{c[h[t]]=t});let u={type:"error",data:"parser error"},l="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),p="function"==typeof ArrayBuffer,d=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,f=({type:t,data:e},s,r)=>l&&e instanceof Blob?s?r(e):y(e,r):p&&(e instanceof ArrayBuffer||d(e))?s?r(e):y(new Blob([e]),r):r(h[t]+(e||"")),y=(t,e)=>{let s=new FileReader;return s.onload=function(){e("b"+(s.result.split(",")[1]||""))},s.readAsDataURL(t)};function g(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<m.length;t++)_[m.charCodeAt(t)]=t;let b=t=>{let e=.75*t.length,s=t.length,r,i=0,n,o,a,h;"="===t[t.length-1]&&(e--,"="===t[t.length-2]&&e--);let c=new ArrayBuffer(e),u=new Uint8Array(c);for(r=0;r<s;r+=4)n=_[t.charCodeAt(r)],o=_[t.charCodeAt(r+1)],a=_[t.charCodeAt(r+2)],h=_[t.charCodeAt(r+3)],u[i++]=n<<2|o>>4,u[i++]=(15&o)<<4|a>>2,u[i++]=(3&a)<<6|63&h;return c},v="function"==typeof ArrayBuffer,k=(t,e)=>{if("string"!=typeof t)return{type:"message",data:E(t,e)};let s=t.charAt(0);return"b"===s?{type:"message",data:w(t.substring(1),e)}:c[s]?t.length>1?{type:c[s],data:t.substring(1)}:{type:c[s]}:u},w=(t,e)=>v?E(b(t),e):{base64:!0,data:t},E=(t,e)=>"blob"===e?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer,T=(t,e)=>{let s=t.length,r=Array(s),i=0;t.forEach((t,n)=>{f(t,!1,t=>{r[n]=t,++i===s&&e(r.join("\x1e"))})})},A=(t,e)=>{let s=t.split("\x1e"),r=[];for(let t=0;t<s.length;t++){let i=k(s[t],e);if(r.push(i),"error"===i.type)break}return r};function O(t){return t.reduce((t,e)=>t+e.length,0)}function R(t,e){if(t[0].length===e)return t.shift();let s=new Uint8Array(e),r=0;for(let i=0;i<e;i++)s[i]=t[0][r++],r===t[0].length&&(t.shift(),r=0);return t.length&&r<t[0].length&&(t[0]=t[0].slice(r)),s}function C(t){if(t)return function(t){for(var e in C.prototype)t[e]=C.prototype[e];return t}(t)}C.prototype.on=C.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},C.prototype.once=function(t,e){function s(){this.off(t,s),e.apply(this,arguments)}return s.fn=e,this.on(t,s),this},C.prototype.off=C.prototype.removeListener=C.prototype.removeAllListeners=C.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+t];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var i=0;i<r.length;i++)if((s=r[i])===e||s.fn===e){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+t],this},C.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),s=this._callbacks["$"+t],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,i=s.length;r<i;++r)s[r].apply(this,e)}return this},C.prototype.emitReserved=C.prototype.emit,C.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},C.prototype.hasListeners=function(t){return!!this.listeners(t).length};let B="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),x="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function N(t,...e){return e.reduce((e,s)=>(t.hasOwnProperty(s)&&(e[s]=t[s]),e),{})}let S=x.setTimeout,L=x.clearTimeout;function q(t,e){e.useNativeTimers?(t.setTimeoutFn=S.bind(x),t.clearTimeoutFn=L.bind(x)):(t.setTimeoutFn=x.setTimeout.bind(x),t.clearTimeoutFn=x.clearTimeout.bind(x))}function P(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class j extends Error{constructor(t,e,s){super(t),this.description=e,this.context=s,this.type="TransportError"}}class D extends C{constructor(t){super(),this.writable=!1,q(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,s){return super.emitReserved("error",new j(t,e,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){let e=k(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){let t=this.opts.hostname;return -1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){let e=function(t){let e="";for(let s in t)t.hasOwnProperty(s)&&(e.length&&(e+="&"),e+=encodeURIComponent(s)+"="+encodeURIComponent(t[s]));return e}(t);return e.length?"?"+e:""}}class U extends D{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";let e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let t=0;this._polling&&(t++,this.once("pollComplete",function(){--t||e()})),this.writable||(t++,this.once("drain",function(){--t||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){A(t,this.socket.binaryType).forEach(t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,T(t,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=P()),this.supportsBinary||e.sid||(e.b64=1),this.createUri(t,e)}}let I=!1;try{I="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}let F=I;function M(){}class V extends U{constructor(t){if(super(t),"undefined"!=typeof location){let e="https:"===location.protocol,s=location.port;s||(s=e?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||s!==t.port}}doWrite(t,e){let s=this.request({method:"POST",data:t});s.on("success",e),s.on("error",(t,e)=>{this.onError("xhr post error",t,e)})}doPoll(){let t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=t}}class H extends C{constructor(t,e,s){super(),this.createRequest=t,q(this,s),this._opts=s,this._method=s.method||"GET",this._uri=e,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var t;let e=N(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(e);try{s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let t in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(t)&&s.setRequestHeader(t,this._opts.extraHeaders[t])}catch(t){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{s.setRequestHeader("Accept","*/*")}catch(t){}null===(t=this._opts.cookieJar)||void 0===t||t.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var t;3===s.readyState&&(null===(t=this._opts.cookieJar)||void 0===t||t.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},s.send(this._data)}catch(t){this.setTimeoutFn(()=>{this._onError(t)},0);return}"undefined"!=typeof document&&(this._index=H.requestsCount++,H.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=M,t)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete H.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function K(){for(let t in H.requests)H.requests.hasOwnProperty(t)&&H.requests[t].abort()}H.requestsCount=0,H.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",K):"function"==typeof addEventListener&&addEventListener("onpagehide"in x?"pagehide":"unload",K,!1));let W=function(){let t=z({xdomain:!1});return t&&null!==t.responseType}();class Y extends V{constructor(t){super(t);let e=t&&t.forceBase64;this.supportsBinary=W&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new H(z,this.uri(),t)}}function z(t){let e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||F))return new XMLHttpRequest}catch(t){}if(!e)try{return new x[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}let J="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class $ extends D{get name(){return"websocket"}doOpen(){let t=this.uri(),e=this.opts.protocols,s=J?{}:N(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,s)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let s=t[e],r=e===t.length-1;f(s,this.supportsBinary,t=>{try{this.doWrite(s,t)}catch(t){}r&&B(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=P()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}let Q=x.WebSocket||x.MozWebSocket;class X extends ${createSocket(t,e,s){return J?new Q(t,e,s):e?new Q(t,e):new Q(t)}doWrite(t,e){this.ws.send(e)}}class G extends D{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{let e=function(t,e){i||(i=new TextDecoder);let s=[],r=0,n=-1,o=!1;return new TransformStream({transform(a,h){for(s.push(a);;){if(0===r){if(1>O(s))break;let t=R(s,1);o=(128&t[0])==128,r=(n=127&t[0])<126?3:126===n?1:2}else if(1===r){if(2>O(s))break;let t=R(s,2);n=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),r=3}else if(2===r){if(8>O(s))break;let t=R(s,8),e=new DataView(t.buffer,t.byteOffset,t.length),i=e.getUint32(0);if(i>2097151){h.enqueue(u);break}n=4294967296*i+e.getUint32(4),r=3}else{if(O(s)<n)break;let t=R(s,n);h.enqueue(k(o?t:i.decode(t),e)),r=0}if(0===n||n>t){h.enqueue(u);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=t.readable.pipeThrough(e).getReader(),n=new TransformStream({transform(t,e){var s;s=s=>{let r;let i=s.length;if(i<126)new DataView((r=new Uint8Array(1)).buffer).setUint8(0,i);else if(i<65536){let t=new DataView((r=new Uint8Array(3)).buffer);t.setUint8(0,126),t.setUint16(1,i)}else{let t=new DataView((r=new Uint8Array(9)).buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(i))}t.data&&"string"!=typeof t.data&&(r[0]|=128),e.enqueue(r),e.enqueue(s)},l&&t.data instanceof Blob?t.data.arrayBuffer().then(g).then(s):p&&(t.data instanceof ArrayBuffer||d(t.data))?s(g(t.data)):f(t,!1,t=>{r||(r=new TextEncoder),s(r.encode(t))})}});n.readable.pipeTo(t.writable),this._writer=n.writable.getWriter();let o=()=>{s.read().then(({done:t,value:e})=>{t||(this.onPacket(e),o())}).catch(t=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let s=t[e],r=e===t.length-1;this._writer.write(s).then(()=>{r&&B(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null===(t=this._transport)||void 0===t||t.close()}}let Z={websocket:X,webtransport:G,polling:Y},tt=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,te=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ts(t){if(t.length>8e3)throw"URI too long";let e=t,s=t.indexOf("["),r=t.indexOf("]");-1!=s&&-1!=r&&(t=t.substring(0,s)+t.substring(s,r).replace(/:/g,";")+t.substring(r,t.length));let i=tt.exec(t||""),n={},o=14;for(;o--;)n[te[o]]=i[o]||"";return -1!=s&&-1!=r&&(n.source=e,n.host=n.host.substring(1,n.host.length-1).replace(/;/g,":"),n.authority=n.authority.replace("[","").replace("]","").replace(/;/g,":"),n.ipv6uri=!0),n.pathNames=function(t,e){let s=e.replace(/\/{2,9}/g,"/").split("/");return("/"==e.slice(0,1)||0===e.length)&&s.splice(0,1),"/"==e.slice(-1)&&s.splice(s.length-1,1),s}(0,n.path),n.queryKey=function(t,e){let s={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,r){e&&(s[e]=r)}),s}(0,n.query),n}let tr="function"==typeof addEventListener&&"function"==typeof removeEventListener,ti=[];tr&&addEventListener("offline",()=>{ti.forEach(t=>t())},!1);class tn extends C{constructor(t,e){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&"object"==typeof t&&(e=t,t=null),t){let s=ts(t);e.hostname=s.host,e.secure="https"===s.protocol||"wss"===s.protocol,e.port=s.port,s.query&&(e.query=s.query)}else e.host&&(e.hostname=ts(e.host).host);q(this,e),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(t=>{let e=t.prototype.name;this.transports.push(e),this._transportsByName[e]=t}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let e={},s=t.split("&");for(let t=0,r=s.length;t<r;t++){let r=s[t].split("=");e[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return e}(this.opts.query)),tr&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ti.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);let s=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](s)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let t=this.opts.rememberUpgrade&&tn.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",tn.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){let s=this.writeBuffer[e].data;if(s&&(t+="string"==typeof s?function(t){let e=0,s=0;for(let r=0,i=t.length;r<i;r++)(e=t.charCodeAt(r))<128?s+=1:e<2048?s+=2:e<55296||e>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),e>0&&t>this._maxPayload)return this.writeBuffer.slice(0,e);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,B(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,s){return this._sendPacket("message",t,e,s),this}send(t,e,s){return this._sendPacket("message",t,e,s),this}_sendPacket(t,e,s,r){if("function"==typeof e&&(r=e,e=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let i={type:t,data:e,options:s};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},s=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():t()}):this.upgrading?s():t()),this}_onError(t){if(tn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),tr&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let t=ti.indexOf(this._offlineEventListener);-1!==t&&ti.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}tn.protocol=4;class to extends tn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),s=!1;tn.priorWebsocketSuccess=!1;let r=()=>{s||(e.send([{type:"ping",data:"probe"}]),e.once("packet",t=>{if(!s){if("pong"===t.type&&"probe"===t.data)this.upgrading=!0,this.emitReserved("upgrading",e),e&&(tn.priorWebsocketSuccess="websocket"===e.name,this.transport.pause(()=>{s||"closed"===this.readyState||(c(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())}));else{let t=Error("probe error");t.transport=e.name,this.emitReserved("upgradeError",t)}}}))};function i(){s||(s=!0,c(),e.close(),e=null)}let n=t=>{let s=Error("probe error: "+t);s.transport=e.name,i(),this.emitReserved("upgradeError",s)};function o(){n("transport closed")}function a(){n("socket closed")}function h(t){e&&t.name!==e.name&&i()}let c=()=>{e.removeListener("open",r),e.removeListener("error",n),e.removeListener("close",o),this.off("close",a),this.off("upgrading",h)};e.once("open",r),e.once("error",n),e.once("close",o),this.once("close",a),this.once("upgrading",h),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn(()=>{s||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){let e=[];for(let s=0;s<t.length;s++)~this.transports.indexOf(t[s])&&e.push(t[s]);return e}}class ta extends to{constructor(t,e={}){let s="object"==typeof t?t:e;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(t=>Z[t]).filter(t=>!!t)),super(t,s)}}ta.protocol;let th="function"==typeof ArrayBuffer,tc=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,tu=Object.prototype.toString,tl="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===tu.call(Blob),tp="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===tu.call(File);function td(t){return th&&(t instanceof ArrayBuffer||tc(t))||tl&&t instanceof Blob||tp&&t instanceof File}let tf=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ty=5;(n=o||(o={}))[n.CONNECT=0]="CONNECT",n[n.DISCONNECT=1]="DISCONNECT",n[n.EVENT=2]="EVENT",n[n.ACK=3]="ACK",n[n.CONNECT_ERROR=4]="CONNECT_ERROR",n[n.BINARY_EVENT=5]="BINARY_EVENT",n[n.BINARY_ACK=6]="BINARY_ACK";class tg{constructor(t){this.replacer=t}encode(t){return(t.type===o.EVENT||t.type===o.ACK)&&function t(e,s){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let s=0,r=e.length;s<r;s++)if(t(e[s]))return!0;return!1}if(td(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1==arguments.length)return t(e.toJSON(),!0);for(let s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t(e[s]))return!0;return!1}(t)?this.encodeAsBinary({type:t.type===o.EVENT?o.BINARY_EVENT:o.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===o.BINARY_EVENT||t.type===o.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){let e=function(t){let e=[],s=t.data;return t.data=function t(e,s){if(!e)return e;if(td(e)){let t={_placeholder:!0,num:s.length};return s.push(e),t}if(Array.isArray(e)){let r=Array(e.length);for(let i=0;i<e.length;i++)r[i]=t(e[i],s);return r}if("object"==typeof e&&!(e instanceof Date)){let r={};for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=t(e[i],s));return r}return e}(s,e),t.attachments=e.length,{packet:t,buffers:e}}(t),s=this.encodeAsString(e.packet),r=e.buffers;return r.unshift(s),r}}function tm(t){return"[object Object]"===Object.prototype.toString.call(t)}class t_ extends C{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(e=this.decodeString(t)).type===o.BINARY_EVENT;s||e.type===o.BINARY_ACK?(e.type=s?o.EVENT:o.ACK,this.reconstructor=new tb(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(td(t)||t.base64){if(this.reconstructor)(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+t)}decodeString(t){let e=0,s={type:Number(t.charAt(0))};if(void 0===o[s.type])throw Error("unknown packet type "+s.type);if(s.type===o.BINARY_EVENT||s.type===o.BINARY_ACK){let r=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);let i=t.substring(r,e);if(i!=Number(i)||"-"!==t.charAt(e))throw Error("Illegal attachments");s.attachments=Number(i)}if("/"===t.charAt(e+1)){let r=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);s.nsp=t.substring(r,e)}else s.nsp="/";let r=t.charAt(e+1);if(""!==r&&Number(r)==r){let r=e+1;for(;++e;){let s=t.charAt(e);if(null==s||Number(s)!=s){--e;break}if(e===t.length)break}s.id=Number(t.substring(r,e+1))}if(t.charAt(++e)){let r=this.tryParse(t.substr(e));if(t_.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return s}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(t){return!1}}static isPayloadValid(t,e){switch(t){case o.CONNECT:return tm(e);case o.DISCONNECT:return void 0===e;case o.CONNECT_ERROR:return"string"==typeof e||tm(e);case o.EVENT:case o.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===tf.indexOf(e[0]));case o.ACK:case o.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class tb{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e,s;let t=(e=this.reconPack,s=this.buffers,e.data=function t(e,s){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<s.length)return s[e.num];throw Error("illegal attachments")}if(Array.isArray(e))for(let r=0;r<e.length;r++)e[r]=t(e[r],s);else if("object"==typeof e)for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(e[r]=t(e[r],s));return e}(e.data,s),delete e.attachments,e);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function tv(t,e,s){return t.on(e,s),function(){t.off(e,s)}}let tk=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class tw extends C{constructor(t,e,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let t=this.io;this.subs=[tv(t,"open",this.onopen.bind(this)),tv(t,"packet",this.onpacket.bind(this)),tv(t,"error",this.onerror.bind(this)),tv(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var s,r,i;if(tk.hasOwnProperty(t))throw Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;let n={type:o.EVENT,data:e};if(n.options={},n.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){let t=this.ids++,s=e.pop();this._registerAckCallback(t,s),n.id=t}let a=null===(r=null===(s=this.io.engine)||void 0===s?void 0:s.transport)||void 0===r?void 0:r.writable,h=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a||(h?(this.notifyOutgoingListeners(n),this.packet(n)):this.sendBuffer.push(n)),this.flags={},this}_registerAckCallback(t,e){var s;let r=null!==(s=this.flags.timeout)&&void 0!==s?s:this._opts.ackTimeout;if(void 0===r){this.acks[t]=e;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let e=0;e<this.sendBuffer.length;e++)this.sendBuffer[e].id===t&&this.sendBuffer.splice(e,1);e.call(this,Error("operation has timed out"))},r),n=(...t)=>{this.io.clearTimeoutFn(i),e.apply(this,t)};n.withError=!0,this.acks[t]=n}emitWithAck(t,...e){return new Promise((s,r)=>{let i=(t,e)=>t?r(t):s(e);i.withError=!0,e.push(i),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((t,...r)=>{if(s===this._queue[0])return null!==t?s.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(t)):(this._queue.shift(),e&&e(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;let e=this._queue[0];(!e.pending||t)&&(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:o.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(e=>String(e.id)===t)){let e=this.acks[t];delete this.acks[t],e.withError&&e.call(this,Error("socket has been disconnected"))}})}onpacket(t){if(!(t.nsp!==this.nsp))switch(t.type){case o.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case o.EVENT:case o.BINARY_EVENT:this.onevent(t);break;case o.ACK:case o.BINARY_ACK:this.onack(t);break;case o.DISCONNECT:this.ondisconnect();break;case o.CONNECT_ERROR:this.destroy();let e=Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}onevent(t){let e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){let e=this,s=!1;return function(...r){s||(s=!0,e.packet({type:o.ACK,id:t,data:r}))}}onack(t){let e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:o.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){let e=this._anyListeners;for(let s=0;s<e.length;s++)if(t===e[s]){e.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){let e=this._anyOutgoingListeners;for(let s=0;s<e.length;s++)if(t===e[s]){e.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let e of this._anyOutgoingListeners.slice())e.apply(this,t.data)}}function tE(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}tE.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),s=Math.floor(e*this.jitter*t);t=(1&Math.floor(10*e))==0?t-s:t+s}return 0|Math.min(t,this.max)},tE.prototype.reset=function(){this.attempts=0},tE.prototype.setMin=function(t){this.ms=t},tE.prototype.setMax=function(t){this.max=t},tE.prototype.setJitter=function(t){this.jitter=t};class tT extends C{constructor(t,e){var s;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,q(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(s=e.randomizationFactor)&&void 0!==s?s:.5),this.backoff=new tE({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;let r=e.parser||a;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null===(e=this.backoff)||void 0===e||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null===(e=this.backoff)||void 0===e||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null===(e=this.backoff)||void 0===e||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new ta(this.uri,this.opts);let e=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=tv(e,"open",function(){s.onopen(),t&&t()}),i=e=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",e),t?t(e):this.maybeReconnectOnOpen()},n=tv(e,"error",i);if(!1!==this._timeout){let t=this._timeout,s=this.setTimeoutFn(()=>{r(),i(Error("timeout")),e.close()},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(n),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let t=this.engine;this.subs.push(tv(t,"ping",this.onping.bind(this)),tv(t,"data",this.ondata.bind(this)),tv(t,"error",this.onerror.bind(this)),tv(t,"close",this.onclose.bind(this)),tv(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}ondecoded(t){B(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let s=this.nsps[t];return s?this._autoConnect&&!s.active&&s.connect():(s=new tw(this,t,e),this.nsps[t]=s),s}_destroy(t){for(let t of Object.keys(this.nsps))if(this.nsps[t].active)return;this._close()}_packet(t){let e=this.encoder.encode(t);for(let s=0;s<e.length;s++)this.engine.write(e[s],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var s;this.cleanup(),null===(s=this.engine)||void 0===s||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let e=this.backoff.duration();this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!t.skipReconnect&&(this.emitReserved("reconnect_attempt",t.backoff.attempts),t.skipReconnect||t.open(e=>{e?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",e)):t.onreconnect()}))},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}let tA={};function tO(t,e){let s;"object"==typeof t&&(e=t,t=void 0);let r=function(t,e="",s){let r=t;s=s||"undefined"!=typeof location&&location,null==t&&(t=s.protocol+"//"+s.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?s.protocol+t:s.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==s?s.protocol+"//"+t:"https://"+t),r=ts(t)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+e,r.href=r.protocol+"://"+i+(s&&s.port===r.port?"":":"+r.port),r}(t,(e=e||{}).path||"/socket.io"),i=r.source,n=r.id,o=r.path,a=tA[n]&&o in tA[n].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?s=new tT(i,e):(tA[n]||(tA[n]=new tT(i,e)),s=tA[n]),r.query&&!e.query&&(e.query=r.queryKey),s.socket(r.path,e)}Object.assign(tO,{Manager:tT,Socket:tw,io:tO,connect:tO})}}]);