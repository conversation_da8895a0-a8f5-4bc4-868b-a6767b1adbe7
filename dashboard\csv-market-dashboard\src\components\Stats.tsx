"use client";

import React from 'react';

interface ConnectionStats {
  totalInstruments: number;
  connectedInstruments: number;
  lastUpdate: Date | null;
  cacheSize: number;
  connectionUptime: number;
  messagesReceived: number;
  reconnectAttempts: number;
  isAutoSaving: boolean;
}

interface StatsProps {
  totalInstruments: number;
  filteredInstruments: number;
  marketDataCount: number;
  connected: boolean;
  connectionStats?: ConnectionStats;
}

const Stats: React.FC<StatsProps> = ({
  totalInstruments,
  filteredInstruments,
  marketDataCount,
  connected,
  connectionStats,
}) => {
  const formatUptime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatLastUpdate = (date: Date | null): string => {
    if (!date) return 'Never';
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;
    return date.toLocaleTimeString();
  };

  const basicStats = [
    {
      label: 'Total Instruments',
      value: totalInstruments.toLocaleString(),
      icon: '📊',
      color: 'bg-blue-500',
      description: 'Available instruments',
    },
    {
      label: 'Filtered Results',
      value: filteredInstruments.toLocaleString(),
      icon: '🔍',
      color: 'bg-purple-500',
      description: 'Matching filters',
    },
    {
      label: 'Live Data',
      value: marketDataCount.toLocaleString(),
      icon: '📈',
      color: connected ? 'bg-green-500' : 'bg-gray-500',
      description: connectionStats ? `${connectionStats.connectedInstruments} active` : 'Market data points',
    },
    {
      label: 'Connection',
      value: connected ? 'Active' : 'Inactive',
      icon: connected ? '🟢' : '🔴',
      color: connected ? 'bg-green-500' : 'bg-red-500',
      description: connectionStats ? `${connectionStats.messagesReceived} messages` : 'WebSocket status',
    },
  ];

  const enhancedStats = connectionStats ? [
    {
      label: 'Cache Size',
      value: connectionStats.cacheSize.toLocaleString(),
      icon: '💾',
      color: 'bg-indigo-500',
      description: connectionStats.isAutoSaving ? 'Auto-saving' : 'Manual save',
    },
    {
      label: 'Last Update',
      value: formatLastUpdate(connectionStats.lastUpdate),
      icon: '🕒',
      color: 'bg-orange-500',
      description: 'Data freshness',
    },
    {
      label: 'Uptime',
      value: formatUptime(connectionStats.connectionUptime),
      icon: '⏱️',
      color: 'bg-teal-500',
      description: 'Connection stability',
    },
    {
      label: 'Reconnects',
      value: connectionStats.reconnectAttempts.toString(),
      icon: connectionStats.reconnectAttempts > 0 ? '🔄' : '✅',
      color: connectionStats.reconnectAttempts > 0 ? 'bg-yellow-500' : 'bg-green-500',
      description: 'Connection reliability',
    },
  ] : [];

  return (
    <div className="space-y-6 mb-6">
      {/* Basic Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {basicStats.map((stat, index) => (
          <div key={index} className="glass rounded-xl shadow-lg p-4 card-hover">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
              <div className="text-2xl ml-3">{stat.icon}</div>
            </div>
            <div className={`mt-3 h-1 rounded-full ${stat.color}`} />
          </div>
        ))}
      </div>

      {/* Enhanced Stats (when available) */}
      {enhancedStats.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <span className="text-xl mr-2">⚡</span>
            Real-time Connection Stats
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {enhancedStats.map((stat, index) => (
              <div key={index} className="glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-xl font-bold text-gray-900 mt-1">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                  </div>
                  <div className="text-xl ml-3">{stat.icon}</div>
                </div>
                <div className={`mt-3 h-1 rounded-full ${stat.color}`} />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Stats;
