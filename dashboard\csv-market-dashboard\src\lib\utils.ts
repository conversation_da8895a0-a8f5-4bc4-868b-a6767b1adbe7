import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format number as Indian currency
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value)
}

/**
 * Format number with Indian number system (lakhs, crores)
 */
export function formatIndianNumber(value: number): string {
  if (value >= 10000000) {
    return `${(value / 10000000).toFixed(2)} Cr`
  } else if (value >= 100000) {
    return `${(value / 100000).toFixed(2)} L`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(2)} K`
  }
  return value.toString()
}

/**
 * Format percentage with proper sign
 */
export function formatPercentage(value: number): string {
  const sign = value >= 0 ? '+' : ''
  return `${sign}${value.toFixed(2)}%`
}

/**
 * Get color class based on value (green for positive, red for negative)
 */
export function getValueColor(value: number): string {
  if (value > 0) return 'text-green-600'
  if (value < 0) return 'text-red-600'
  return 'text-gray-600'
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * Deep clone object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T
  if (typeof obj === 'object') {
    const clonedObj = {} as { [key: string]: any }
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj as T
  }
  return obj
}

/**
 * Generate unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * Sleep function for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Retry function with exponential backoff
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxAttempts) {
        throw lastError
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1)
      await sleep(delay)
    }
  }
  
  throw lastError!
}

/**
 * Safe JSON parse with fallback
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json)
  } catch {
    return fallback
  }
}

/**
 * Check if value is empty (null, undefined, empty string, empty array, empty object)
 */
export function isEmpty(value: any): boolean {
  if (value == null) return true
  if (typeof value === 'string') return value.trim() === ''
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * Format value with fallback to dash for empty values
 */
export function formatValueWithFallback(value: any, formatter?: (val: any) => string): string {
  if (isEmpty(value) || value === 0) return '-'
  return formatter ? formatter(value) : String(value)
}

/**
 * Market data formatting utilities with consistent dash fallbacks
 */
export const MarketFormatters = {
  /**
   * Format price with currency symbol
   */
  price: (price: number | undefined): string => {
    if (!price || price <= 0) return '-'
    return `₹${price.toFixed(2)}`
  },

  /**
   * Format number with locale formatting
   */
  number: (num: number | undefined): string => {
    if (num === undefined || num === null) return '-'
    return num.toLocaleString()
  },

  /**
   * Format percentage with sign
   */
  percentage: (value: number | undefined): string => {
    if (!value || value === 0) return '-'
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(2)}%`
  },

  /**
   * Format change with price and percentage
   */
  change: (change: number | undefined, changePercent: number | undefined): string => {
    if (!change || change === 0) return '-'
    const sign = change > 0 ? '+' : ''
    const percentStr = changePercent ? ` (${sign}${changePercent.toFixed(2)}%)` : ''
    return `${sign}${change.toFixed(2)}${percentStr}`
  },

  /**
   * Format volume with Indian number system
   */
  volume: (volume: number | undefined): string => {
    if (volume === undefined || volume === null) return '-'
    return formatIndianNumber(volume)
  },

  /**
   * Format time from timestamp
   */
  time: (timestamp: number | undefined): string => {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleTimeString()
  },

  /**
   * Format date from timestamp
   */
  date: (timestamp: number | undefined): string => {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleDateString()
  },

  /**
   * Format bid/ask with quantity
   */
  bidAsk: (price: number | undefined, qty: number | undefined): string => {
    if (!price || price <= 0) return '-'
    const qtyStr = qty ? ` (${qty})` : ''
    return `₹${price.toFixed(2)}${qtyStr}`
  }
}

/**
 * Capitalize first letter of string
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

/**
 * Convert string to title case
 */
export function toTitleCase(str: string): string {
  return str.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  )
}

/**
 * Truncate string with ellipsis
 */
export function truncate(str: string, length: number): string {
  if (str.length <= length) return str
  return str.slice(0, length) + '...'
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

/**
 * Get relative time string (e.g., "2 minutes ago")
 */
export function getRelativeTime(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`
  
  return date.toLocaleDateString()
}

/**
 * Check if current time is within market hours
 */
export function isMarketHours(): boolean {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  const currentTime = hours * 60 + minutes
  
  // Market hours: 9:15 AM to 3:30 PM (IST)
  const marketOpen = 9 * 60 + 15  // 9:15 AM
  const marketClose = 15 * 60 + 30 // 3:30 PM
  
  // Check if it's a weekday (Monday = 1, Sunday = 0)
  const dayOfWeek = now.getDay()
  const isWeekday = dayOfWeek >= 1 && dayOfWeek <= 5
  
  return isWeekday && currentTime >= marketOpen && currentTime <= marketClose
}

/**
 * Calculate option Greeks (simplified)
 */
export function calculateImpliedVolatility(
  optionPrice: number,
  spotPrice: number,
  strikePrice: number,
  timeToExpiry: number,
  riskFreeRate: number = 0.06
): number {
  // Simplified Black-Scholes implied volatility calculation
  // This is a basic approximation - use proper financial libraries for production
  const moneyness = spotPrice / strikePrice
  const timeValue = optionPrice / spotPrice
  
  // Basic approximation formula
  const iv = Math.sqrt(2 * Math.PI / timeToExpiry) * timeValue / moneyness
  
  return Math.max(0.01, Math.min(5.0, iv)) // Clamp between 1% and 500%
}
