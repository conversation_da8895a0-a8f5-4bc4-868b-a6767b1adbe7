/**
 * Custom hook for Market Data Management
 * Provides a clean interface to the Zustand market store
 */

import { useEffect } from 'react';
import useMarketStore from '@/store/marketStore';

export const useMarketData = () => {
  const store = useMarketStore();

  // Hydrate from Redis on mount
  useEffect(() => {
    fetch('/api/cache/all-latest')
      .then(res => res.json())
      .then(({ data }) => {
        if (Array.isArray(data)) store.hydrateFromRedis(data);
      });
  }, [store.hydrateFromRedis]);

  // WebSocket tick handling (example, adapt as needed)
  // ws.on('tick', tick => store.updateMarketData(tick));
  // ws.on('batch', ticks => store.updateMarketDataBatch(ticks));

  return {
    marketData: Object.values(store.marketData),
    updateMarketData: store.updateMarketData,
    updateMarketDataBatch: store.updateMarketDataBatch,
    hydrateFromRedis: store.hydrateFromRedis,
    // ...other state/actions as needed
  };
};

export default useMarketData;
