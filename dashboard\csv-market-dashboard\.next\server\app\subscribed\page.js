(()=>{var e={};e.id=200,e.ids=[200],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},8893:e=>{"use strict";e.exports=require("buffer")},1282:e=>{"use strict";e.exports=require("child_process")},4770:e=>{"use strict";e.exports=require("crypto")},7702:e=>{"use strict";e.exports=require("events")},2048:e=>{"use strict";e.exports=require("fs")},2615:e=>{"use strict";e.exports=require("http")},8791:e=>{"use strict";e.exports=require("https")},8216:e=>{"use strict";e.exports=require("net")},9801:e=>{"use strict";e.exports=require("os")},6162:e=>{"use strict";e.exports=require("stream")},2452:e=>{"use strict";e.exports=require("tls")},4175:e=>{"use strict";e.exports=require("tty")},7360:e=>{"use strict";e.exports=require("url")},1764:e=>{"use strict";e.exports=require("util")},1568:e=>{"use strict";e.exports=require("zlib")},3185:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o}),s(2311),s(2029),s(5866);var r=s(3191),a=s(8716),i=s(7922),n=s.n(i),l=s(5231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o=["",{children:["subscribed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2311)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\subscribed\\page.tsx"],x="/subscribed/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/subscribed/page",pathname:"/subscribed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},9554:(e,t,s)=>{Promise.resolve().then(s.bind(s,1655))},1655:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(326),a=s(7577);s(3687);let i=(0,a.createContext)(void 0);var n=s(1223);function l(){let{marketData:e,isConnected:t,error:s,lastUpdate:l,totalInstruments:c}=function(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useWebSocketContext must be used within a WebSocketProvider");return e}(),[o,d]=(0,a.useState)(""),[x,u]=(0,a.useState)("symbol"),[p,m]=(0,a.useState)("asc"),[h,g]=(0,a.useState)("all"),b=e.filter(e=>{let t=e.symbol.toLowerCase().includes(o.toLowerCase()),s="all"===h||"options"===h&&(e.symbol.includes("-CE")||e.symbol.includes("-PE"))||"futures"===h&&!e.symbol.includes("-CE")&&!e.symbol.includes("-PE");return t&&s}).sort((e,t)=>{let s,r;switch(x){case"symbol":default:s=e.symbol,r=t.symbol;break;case"ltp":s=e.ltp||0,r=t.ltp||0;break;case"change":s=e.change||0,r=t.change||0;break;case"volume":s=e.volume||0,r=t.volume||0;break;case"openInterest":s=e.openInterest||0,r=t.openInterest||0}return"string"==typeof s?"asc"===p?s.localeCompare(r):r.localeCompare(s):"asc"===p?s-r:r-s}),y=e=>{x===e?m("asc"===p?"desc":"asc"):(u(e),m("asc"))},v=n.A1.number,j=n.A1.price,f=e=>e?e>0?"text-green-600":e<0?"text-red-600":"text-gray-500":"text-gray-500";return(0,r.jsxs)("div",{className:"container mx-auto p-4 max-w-7xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h1",{className:"text-3xl font-bold",children:"NSE Derivatives - Subscribed Data Dashboard"}),(0,r.jsxs)("div",{className:"space-x-3",children:[r.jsx("a",{href:"/",className:"inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 transition-colors",children:"\uD83C\uDFE0 Main Dashboard"}),r.jsx("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 Option Chain"})]})]}),r.jsx("div",{className:"mb-6 p-4 bg-gray-100 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("div",{className:`inline-block px-3 py-1 rounded ${t?"bg-green-500":"bg-red-500"} text-white`,children:t?"\uD83D\uDFE2 Connected":"\uD83D\uDD34 Disconnected"}),s&&(0,r.jsxs)("div",{className:"inline-block px-3 py-1 rounded bg-red-100 text-red-800 text-sm",children:["Error: ",s]}),(0,r.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCCA Total Subscribed: ",e.length," instruments"]}),(0,r.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDCC8 Live Data: ",e.filter(e=>e.ltp>0).length," active"]}),(0,r.jsxs)("span",{className:"text-gray-700",children:["\uD83D\uDD22 With OI: ",e.filter(e=>e.openInterest&&e.openInterest>0).length," contracts"]})]}),r.jsx("div",{className:"text-sm text-gray-600",children:"Server Auto-Subscription: NSE OPTIDX + FUTIDX Only"})]})}),r.jsx("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search Instruments"}),r.jsx("input",{type:"text",placeholder:"Search by symbol...",value:o,onChange:e=>d(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Filter by Type"}),(0,r.jsxs)("select",{value:h,onChange:e=>g(e.target.value),className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500",children:[r.jsx("option",{value:"all",children:"All Instruments"}),r.jsx("option",{value:"options",children:"Options (CE/PE)"}),r.jsx("option",{value:"futures",children:"Futures"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Showing Results"}),(0,r.jsxs)("div",{className:"p-2 bg-gray-50 rounded-md text-sm",children:[b.length," of ",e.length," instruments"]})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsxs)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>y("symbol"),children:["Symbol ","symbol"===x&&("asc"===p?"↑":"↓")]}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Exchange"}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>y("ltp"),children:["LTP ","ltp"===x&&("asc"===p?"↑":"↓")]}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>y("change"),children:["Change ","change"===x&&("asc"===p?"↑":"↓")]}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>y("volume"),children:["Volume ","volume"===x&&("asc"===p?"↑":"↓")]}),(0,r.jsxs)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100",onClick:()=>y("openInterest"),children:["Open Interest ","openInterest"===x&&("asc"===p?"↑":"↓")]}),r.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"High/Low"}),r.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Open/Close"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:b.slice(0,100).map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.symbol}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.exchange}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium",children:j(e.ltp)}),r.jsx("td",{className:`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${f(e.change)}`,children:e.change?(0,r.jsxs)(r.Fragment,{children:[e.change>0?"+":"",e.change.toFixed(2),r.jsx("br",{}),(0,r.jsxs)("span",{className:"text-xs",children:["(",e.changePercent>0?"+":"",e.changePercent.toFixed(2),"%)"]})]}):"-"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:v(e.volume)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-blue-600",children:v(e.openInterest)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.high?(0,r.jsxs)(r.Fragment,{children:[j(e.high),r.jsx("br",{}),r.jsx("span",{className:"text-xs",children:j(e.low)})]}):"-"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-500",children:e.open?(0,r.jsxs)(r.Fragment,{children:[j(e.open),r.jsx("br",{}),r.jsx("span",{className:"text-xs",children:j(e.close)})]}):"-"})]},e.securityId))})]})}),0===b.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("p",{className:"text-gray-500",children:"No subscribed data available"}),r.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"The server will automatically subscribe to NSE OPTIDX and FUTIDX instruments"})]}),b.length>100&&(0,r.jsxs)("div",{className:"bg-gray-50 px-6 py-3 text-sm text-gray-600",children:["Showing first 100 of ",b.length," results. Use search to narrow down."]})]})]})}},2311:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(8570).createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\subscribed\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[276,314,432,54],()=>s(3185));module.exports=r})();