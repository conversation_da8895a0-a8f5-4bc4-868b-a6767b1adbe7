/**
 * Performance Monitoring and Optimization Utilities
 */

import { PERFORMANCE_CONFIG } from './constants'

// Performance metrics interface
interface PerformanceMetrics {
  memoryUsage: NodeJS.MemoryUsage
  cpuUsage: NodeJS.CpuUsage
  uptime: number
  timestamp: number
}

// Memory monitoring
export class MemoryMonitor {
  private static instance: MemoryMonitor
  private metrics: PerformanceMetrics[] = []
  private warningThreshold = PERFORMANCE_CONFIG.MEMORY_WARNING_THRESHOLD

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor()
    }
    return MemoryMonitor.instance
  }

  getCurrentMetrics(): PerformanceMetrics {
    return {
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime: process.uptime(),
      timestamp: Date.now(),
    }
  }

  recordMetrics(): void {
    const metrics = this.getCurrentMetrics()
    this.metrics.push(metrics)

    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics.shift()
    }

    // Check for memory warnings
    if (metrics.memoryUsage.heapUsed > this.warningThreshold) {
      console.warn(`⚠️ High memory usage detected: ${this.formatBytes(metrics.memoryUsage.heapUsed)}`)
    }
  }

  getMetricsHistory(): PerformanceMetrics[] {
    return [...this.metrics]
  }

  private formatBytes(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  startMonitoring(interval: number = 30000): void {
    setInterval(() => {
      this.recordMetrics()
    }, interval)
  }
}

// Request performance tracking
export class RequestTracker {
  private static requests = new Map<string, number>()
  private static completedRequests: Array<{
    id: string
    duration: number
    timestamp: number
    endpoint: string
  }> = []

  static startRequest(id: string, endpoint: string): void {
    this.requests.set(id, Date.now())
  }

  static endRequest(id: string, endpoint: string): number {
    const startTime = this.requests.get(id)
    if (!startTime) return 0

    const duration = Date.now() - startTime
    this.requests.delete(id)

    // Record completed request
    this.completedRequests.push({
      id,
      duration,
      timestamp: Date.now(),
      endpoint,
    })

    // Keep only last 1000 requests
    if (this.completedRequests.length > 1000) {
      this.completedRequests.shift()
    }

    // Log slow requests
    if (duration > PERFORMANCE_CONFIG.NETWORK_TIMEOUT_WARNING) {
      console.warn(`🐌 Slow request detected: ${endpoint} took ${duration}ms`)
    }

    return duration
  }

  static getAverageResponseTime(endpoint?: string): number {
    const requests = endpoint
      ? this.completedRequests.filter(r => r.endpoint === endpoint)
      : this.completedRequests

    if (requests.length === 0) return 0

    const totalDuration = requests.reduce((sum, req) => sum + req.duration, 0)
    return totalDuration / requests.length
  }

  static getRequestStats() {
    return {
      activeRequests: this.requests.size,
      completedRequests: this.completedRequests.length,
      averageResponseTime: this.getAverageResponseTime(),
      slowRequests: this.completedRequests.filter(
        r => r.duration > PERFORMANCE_CONFIG.NETWORK_TIMEOUT_WARNING
      ).length,
    }
  }
}

// WebSocket performance monitoring
export class WebSocketMonitor {
  private static connections = new Map<string, {
    connectedAt: number
    messagesSent: number
    messagesReceived: number
    lastActivity: number
  }>()

  static addConnection(id: string): void {
    this.connections.set(id, {
      connectedAt: Date.now(),
      messagesSent: 0,
      messagesReceived: 0,
      lastActivity: Date.now(),
    })
  }

  static removeConnection(id: string): void {
    this.connections.delete(id)
  }

  static recordMessageSent(id: string): void {
    const connection = this.connections.get(id)
    if (connection) {
      connection.messagesSent++
      connection.lastActivity = Date.now()
    }
  }

  static recordMessageReceived(id: string): void {
    const connection = this.connections.get(id)
    if (connection) {
      connection.messagesReceived++
      connection.lastActivity = Date.now()
    }
  }

  static getConnectionStats() {
    const connections = Array.from(this.connections.values())
    const now = Date.now()

    return {
      totalConnections: connections.length,
      totalMessagesSent: connections.reduce((sum, conn) => sum + conn.messagesSent, 0),
      totalMessagesReceived: connections.reduce((sum, conn) => sum + conn.messagesReceived, 0),
      averageConnectionTime: connections.length > 0
        ? connections.reduce((sum, conn) => sum + (now - conn.connectedAt), 0) / connections.length
        : 0,
      activeConnections: connections.filter(
        conn => now - conn.lastActivity < 60000 // Active in last minute
      ).length,
    }
  }
}

// Performance profiler for functions
export function profileFunction<T extends (...args: any[]) => any>(
  fn: T,
  name: string
): T {
  return ((...args: Parameters<T>): ReturnType<T> => {
    const start = performance.now()
    const result = fn(...args)
    const end = performance.now()
    
    console.debug(`🔍 Function ${name} took ${(end - start).toFixed(2)}ms`)
    
    return result
  }) as T
}

// Async function profiler
export function profileAsyncFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  name: string
): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    const start = performance.now()
    const result = await fn(...args)
    const end = performance.now()
    
    console.debug(`🔍 Async function ${name} took ${(end - start).toFixed(2)}ms`)
    
    return result
  }) as T
}

// Memory leak detector
export class MemoryLeakDetector {
  private static snapshots: Array<{
    timestamp: number
    heapUsed: number
    heapTotal: number
  }> = []

  static takeSnapshot(): void {
    const memUsage = process.memoryUsage()
    this.snapshots.push({
      timestamp: Date.now(),
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
    })

    // Keep only last 50 snapshots
    if (this.snapshots.length > 50) {
      this.snapshots.shift()
    }
  }

  static detectLeaks(): boolean {
    if (this.snapshots.length < 10) return false

    const recent = this.snapshots.slice(-10)
    const older = this.snapshots.slice(-20, -10)

    const recentAvg = recent.reduce((sum, s) => sum + s.heapUsed, 0) / recent.length
    const olderAvg = older.reduce((sum, s) => sum + s.heapUsed, 0) / older.length

    // If recent average is 20% higher than older average, potential leak
    const threshold = olderAvg * 1.2
    
    if (recentAvg > threshold) {
      console.warn(`🚨 Potential memory leak detected! Recent avg: ${recentAvg}, Older avg: ${olderAvg}`)
      return true
    }

    return false
  }

  static startMonitoring(interval: number = 60000): void {
    setInterval(() => {
      this.takeSnapshot()
      this.detectLeaks()
    }, interval)
  }
}

// Bundle size analyzer (for client-side)
export function analyzeBundleSize(): void {
  if (typeof window === 'undefined') return

  const scripts = Array.from(document.querySelectorAll('script[src]'))
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))

  console.group('📦 Bundle Analysis')
  console.log(`Scripts: ${scripts.length}`)
  console.log(`Stylesheets: ${styles.length}`)
  
  scripts.forEach((script: any) => {
    console.log(`Script: ${script.src}`)
  })
  
  styles.forEach((style: any) => {
    console.log(`Stylesheet: ${style.href}`)
  })
  console.groupEnd()
}

// Performance observer for client-side metrics
export function initPerformanceObserver(): void {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return

  // Observe navigation timing
  const navObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    entries.forEach((entry) => {
      console.log(`📊 Navigation: ${entry.name} - ${entry.duration.toFixed(2)}ms`)
    })
  })
  navObserver.observe({ entryTypes: ['navigation'] })

  // Observe resource timing
  const resourceObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    entries.forEach((entry) => {
      if (entry.duration > 1000) { // Log slow resources
        console.warn(`🐌 Slow resource: ${entry.name} - ${entry.duration.toFixed(2)}ms`)
      }
    })
  })
  resourceObserver.observe({ entryTypes: ['resource'] })

  // Observe largest contentful paint
  const lcpObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries()
    const lastEntry = entries[entries.length - 1]
    console.log(`🎨 LCP: ${lastEntry.startTime.toFixed(2)}ms`)
  })
  lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
}

// Export performance monitoring utilities
export const Performance = {
  MemoryMonitor,
  RequestTracker,
  WebSocketMonitor,
  MemoryLeakDetector,
  profileFunction,
  profileAsyncFunction,
  analyzeBundleSize,
  initPerformanceObserver,
}
