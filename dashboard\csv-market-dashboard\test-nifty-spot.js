const WebSocket = require('ws');
require('dotenv').config();

/**
 * 🧪 SIMPLE NIFTY SPOT TEST CASE
 * This is a standalone test to debug NIFTY spot subscription
 */

class NiftySpotTest {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.accessToken = process.env.ACCESS_TOKEN;
    this.clientId = process.env.CLIENT_ID;
    
    console.log('🧪 [TEST] NIFTY Spot Test Case Starting...');
    console.log('🔑 Access Token:', this.accessToken ? 'Found' : 'Missing');
    console.log('🔑 Client ID:', this.clientId ? 'Found' : 'Missing');
  }

  async connect() {
    try {
      console.log('🔌 [TEST] Connecting to Dhan WebSocket...');
      
      // ✅ CORRECT: Use the proper Dhan WebSocket URL with query parameters
      const wsUrl = `wss://api-feed.dhan.co?version=2&token=${encodeURIComponent(this.accessToken)}&clientId=${encodeURIComponent(this.clientId)}&authType=2`;

      console.log('🔗 [TEST] WebSocket URL:', wsUrl.replace(this.accessToken, 'HIDDEN_TOKEN'));

      this.ws = new WebSocket(wsUrl, {
        headers: {
          'User-Agent': 'NIFTY-Spot-Test/1.0',
        },
        handshakeTimeout: 10000,
      });

      this.setupEventHandlers();
      
      return new Promise((resolve, reject) => {
        this.ws.on('open', () => {
          this.isConnected = true;
          console.log('✅ [TEST] Connected to Dhan WebSocket');
          resolve();
        });

        this.ws.on('error', (error) => {
          console.error('❌ [TEST] WebSocket connection error:', error);
          reject(error);
        });

        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('Connection timeout'));
          }
        }, 10000);
      });
    } catch (error) {
      console.error('❌ [TEST] Connection failed:', error);
      throw error;
    }
  }

  setupEventHandlers() {
    this.ws.on('message', (data) => {
      this.handleMessage(data);
    });

    this.ws.on('close', (code, reason) => {
      this.isConnected = false;
      console.log(`🔌 [TEST] WebSocket closed: ${code} - ${reason}`);
    });

    this.ws.on('error', (error) => {
      console.error('❌ [TEST] WebSocket error:', error);
    });
  }

  handleMessage(data) {
    try {
      // Convert to Buffer for binary parsing
      let buffer;
      if (Buffer.isBuffer(data)) {
        buffer = data;
      } else if (data instanceof ArrayBuffer) {
        buffer = Buffer.from(data);
      } else if (Array.isArray(data)) {
        buffer = Buffer.concat(data);
      } else {
        console.log('🔍 [TEST] Unknown data type received:', typeof data);
        return;
      }

      console.log(`📦 [TEST] Received packet: Length=${buffer.length} bytes`);

      if (buffer.length >= 8) {
        // Parse basic header
        const responseCode = buffer.readUInt8(0);
        const messageLength = buffer.readUInt16LE(1);
        const exchangeSegment = buffer.readUInt8(3);
        const securityId = buffer.readUInt32LE(4);

        console.log(`📊 [TEST] Packet Details:`);
        console.log(`   Response Code: ${responseCode}`);
        console.log(`   Message Length: ${messageLength}`);
        console.log(`   Exchange Segment: ${exchangeSegment}`);
        console.log(`   Security ID: ${securityId}`);

        // Check if this is NIFTY spot (security ID 13)
        if (securityId === 13) {
          console.log('🎯 [TEST] ✅ NIFTY SPOT DATA RECEIVED!');
          console.log('🎯 [TEST] This is the packet we were looking for!');
          
          // Try to parse LTP if it's a full packet
          if (responseCode === 8 && buffer.length >= 16) {
            const ltp = buffer.readFloatLE(8);
            console.log(`🎯 [TEST] NIFTY LTP: ₹${ltp}`);
          }
        } else {
          console.log(`📊 [TEST] Other instrument: SecurityId=${securityId}`);
        }
      }
    } catch (error) {
      console.error('❌ [TEST] Error handling message:', error);
    }
  }

  subscribeToNiftySpot() {
    if (!this.isConnected) {
      console.error('❌ [TEST] Not connected - cannot subscribe');
      return;
    }

    console.log('🎯 [TEST] Subscribing to NIFTY spot...');

    // Exact subscription format as you specified
    const subscriptionMessage = {
      "RequestCode": 21,
      "InstrumentCount": 1,
      "InstrumentList": [
        {
          "ExchangeSegment": "IDX_I",
          "SecurityId": "13"
        }
      ]
    };

    console.log('📤 [TEST] Sending subscription message:');
    console.log(JSON.stringify(subscriptionMessage, null, 2));

    try {
      this.ws.send(JSON.stringify(subscriptionMessage));
      console.log('✅ [TEST] Subscription message sent successfully');
    } catch (error) {
      console.error('❌ [TEST] Failed to send subscription:', error);
    }
  }

  async runTest() {
    try {
      // Step 1: Connect
      await this.connect();
      
      // Step 2: Wait a moment for connection to stabilize
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Step 3: Subscribe to NIFTY spot
      this.subscribeToNiftySpot();
      
      // Step 4: Wait for data
      console.log('⏰ [TEST] Waiting 30 seconds for NIFTY spot data...');
      await new Promise(resolve => setTimeout(resolve, 30000));
      
      console.log('⏰ [TEST] Test completed - check logs above for NIFTY spot data');
      
    } catch (error) {
      console.error('❌ [TEST] Test failed:', error);
    } finally {
      if (this.ws) {
        this.ws.close();
      }
    }
  }
}

// Run the test
const test = new NiftySpotTest();
test.runTest().then(() => {
  console.log('🏁 [TEST] Test finished');
  process.exit(0);
}).catch((error) => {
  console.error('💥 [TEST] Test crashed:', error);
  process.exit(1);
});
