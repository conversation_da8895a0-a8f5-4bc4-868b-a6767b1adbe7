/**
 * Robust WebSocket Connection Manager
 * Handles connection pooling, reconnection, and proper cleanup
 * FIXED: Enhanced singleton pattern with proper connection sharing
 */

import { io, Socket } from 'socket.io-client'
import { WEBSOCKET_CONFIG, API_CONFIG } from './constants'

interface WebSocketOptions {
  onConnect?: () => void
  onDisconnect?: (reason: string) => void
  onError?: (error: Error) => void
  onReconnect?: (attemptNumber: number) => void
  onMarketData?: (data: any) => void
  onMarketDataBatch?: (data: any[]) => void
}

interface ConnectionStats {
  connected: boolean
  clients: number
  reconnectAttempts: number
  lastConnected: Date | null
  totalMessages: number
  errors: number
  connectionId: string | null
}

class WebSocketManager {
  private static instance: WebSocketManager | null = null
  private socket: Socket | null = null
  private isConnecting = false
  private clientCount = 0
  private listeners = new Map<string, Set<Function>>()
  private clientCallbacks = new Map<string, WebSocketOptions>()
  private stats: ConnectionStats = {
    connected: false,
    clients: 0,
    reconnectAttempts: 0,
    lastConnected: null,
    totalMessages: 0,
    errors: 0,
    connectionId: null,
  }
  private heartbeatInterval: NodeJS.Timeout | null = null
  private cleanupInterval: NodeJS.Timeout | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager()
      console.log('🔧 WebSocketManager: New singleton instance created')
    }
    return WebSocketManager.instance
  }

  // Prevent direct instantiation
  private constructor() {
    console.log('🔧 WebSocketManager: Constructor called')
  }

  /**
   * Get or create WebSocket connection with enhanced singleton behavior
   */
  async connect(options: WebSocketOptions = {}): Promise<Socket> {
    const clientId = this.generateClientId()
    console.log(`🔌 WebSocketManager: Client ${clientId} requesting connection`)

    // Store client callbacks for later use
    this.clientCallbacks.set(clientId, options)

    // Return existing connection if available and connected
    if (this.socket && this.socket.connected) {
      console.log(`✅ WebSocketManager: Reusing existing connection for client ${clientId}`)
      this.addClient(options, clientId)
      return this.socket
    }

    // Prevent multiple simultaneous connection attempts
    if (this.isConnecting) {
      console.log(`⏳ WebSocketManager: Connection in progress, waiting for client ${clientId}`)
      return new Promise((resolve, reject) => {
        const checkConnection = () => {
          if (this.socket && this.socket.connected) {
            console.log(`✅ WebSocketManager: Connection ready for waiting client ${clientId}`)
            this.addClient(options, clientId)
            resolve(this.socket)
          } else if (!this.isConnecting) {
            console.log(`❌ WebSocketManager: Connection failed for waiting client ${clientId}`)
            reject(new Error('Connection failed'))
          } else {
            setTimeout(checkConnection, 100)
          }
        }
        checkConnection()
      })
    }

    console.log(`🚀 WebSocketManager: Creating new connection for client ${clientId}`)
    this.isConnecting = true

    try {
      await this.createConnection(options)
      this.addClient(options, clientId)
      return this.socket!
    } catch (error) {
      this.isConnecting = false
      this.clientCallbacks.delete(clientId)
      console.error(`❌ WebSocketManager: Connection failed for client ${clientId}:`, error)
      throw error
    }
  }

  /**
   * Generate unique client ID for tracking
   */
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Create new WebSocket connection with enhanced error handling
   */
  private async createConnection(options: WebSocketOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      const serverUrl = API_CONFIG.BASE_URL
      console.log(`🔌 WebSocketManager: Creating connection to ${serverUrl}`)

      // Clean up existing connection
      if (this.socket) {
        console.log('🧹 WebSocketManager: Cleaning up existing connection')
        this.socket.removeAllListeners()
        this.socket.disconnect()
      }

      // Create new connection with optimized settings
      this.socket = io(serverUrl, {
        transports: ['websocket', 'polling'],
        upgrade: true,
        rememberUpgrade: false,
        timeout: WEBSOCKET_CONFIG.CONNECTION_TIMEOUT,
        forceNew: false,
        reconnection: true,
        reconnectionAttempts: WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS,
        reconnectionDelay: WEBSOCKET_CONFIG.RECONNECT_INTERVAL,
        reconnectionDelayMax: WEBSOCKET_CONFIG.RECONNECT_INTERVAL * 4,
        randomizationFactor: 0.5,
        autoConnect: true,
      })

      // Connection event handlers
      this.socket.on('connect', () => {
        console.log('✅ WebSocketManager: Connected successfully')
        this.isConnecting = false
        this.stats.connected = true
        this.stats.lastConnected = new Date()
        this.stats.reconnectAttempts = 0
        this.stats.connectionId = this.socket?.id || null
        this.startHeartbeat()

        // Notify all clients
        this.notifyAllClients('onConnect')
        resolve()
      })

      this.socket.on('disconnect', (reason) => {
        console.log(`❌ WebSocketManager: Disconnected - ${reason}`)
        this.stats.connected = false
        this.stats.connectionId = null
        this.stopHeartbeat()

        // Notify all clients
        this.notifyAllClients('onDisconnect', reason)

        // Attempt auto-reconnection for certain disconnect reasons
        if (reason !== 'io client disconnect' && reason !== 'client namespace disconnect') {
          this.scheduleReconnection()
        }
      })

      this.socket.on('connect_error', (error) => {
        console.error('🔥 WebSocketManager: Connection error:', error.message)
        this.stats.errors++
        this.isConnecting = false

        // Notify all clients
        this.notifyAllClients('onError', error)
        reject(error)
      })

      this.socket.on('reconnect', (attemptNumber) => {
        console.log(`🔄 WebSocketManager: Reconnected after ${attemptNumber} attempts`)
        this.stats.reconnectAttempts = attemptNumber
        this.stats.connected = true
        this.stats.lastConnected = new Date()
        this.stats.connectionId = this.socket?.id || null

        // Notify all clients
        this.notifyAllClients('onReconnect', attemptNumber)
      })

      this.socket.on('reconnect_attempt', (attemptNumber) => {
        console.log(`🔄 WebSocketManager: Reconnection attempt ${attemptNumber}`)
        this.stats.reconnectAttempts = attemptNumber
      })

      this.socket.on('reconnect_failed', () => {
        console.error('💥 WebSocketManager: Reconnection failed')
        this.stats.connected = false
        this.isConnecting = false
        this.stats.connectionId = null
      })

      // Market data handlers
      this.socket.on('marketData', (data) => {
        this.stats.totalMessages++
        this.notifyListeners('marketData', data)
      })

      this.socket.on('marketDataBatch', (data) => {
        this.stats.totalMessages += data.length
        this.notifyListeners('marketDataBatch', data)
      })

      // Start cleanup interval
      this.startCleanupInterval()

      // Connection timeout
      setTimeout(() => {
        if (this.isConnecting) {
          this.isConnecting = false
          reject(new Error('Connection timeout'))
        }
      }, WEBSOCKET_CONFIG.CONNECTION_TIMEOUT)
    })
  }

  /**
   * Add client and register listeners with enhanced tracking
   */
  private addClient(options: WebSocketOptions, clientId: string): void {
    this.clientCount++
    this.stats.clients = this.clientCount

    // Register listeners
    if (options.onMarketData) {
      this.addListener('marketData', options.onMarketData)
    }
    if (options.onMarketDataBatch) {
      this.addListener('marketDataBatch', options.onMarketDataBatch)
    }

    console.log(`📊 WebSocketManager: Client ${clientId} added (Total: ${this.clientCount})`)
  }

  /**
   * Notify all clients of connection events
   */
  private notifyAllClients(eventType: keyof WebSocketOptions, data?: any): void {
    this.clientCallbacks.forEach((callbacks, clientId) => {
      try {
        switch (eventType) {
          case 'onConnect':
            callbacks.onConnect?.()
            break
          case 'onDisconnect':
            callbacks.onDisconnect?.(data)
            break
          case 'onError':
            callbacks.onError?.(data)
            break
          case 'onReconnect':
            callbacks.onReconnect?.(data)
            break
        }
      } catch (error) {
        console.error(`❌ WebSocketManager: Error notifying client ${clientId}:`, error)
      }
    })
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnection(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
    }

    const delay = Math.min(
      WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(2, this.stats.reconnectAttempts),
      30000 // Max 30 seconds
    )

    console.log(`🔄 WebSocketManager: Scheduling reconnection in ${delay}ms`)

    this.reconnectTimeout = setTimeout(() => {
      if (!this.stats.connected && this.clientCount > 0) {
        console.log('🔄 WebSocketManager: Attempting auto-reconnection')
        this.connect().catch(error => {
          console.error('❌ WebSocketManager: Auto-reconnection failed:', error)
        })
      }
    }, delay)
  }

  /**
   * Remove client and cleanup listeners with enhanced tracking
   */
  removeClient(options: WebSocketOptions): void {
    if (this.clientCount > 0) {
      this.clientCount--
      this.stats.clients = this.clientCount

      // Find and remove client callback
      let removedClientId = 'unknown'
      for (const [clientId, callbacks] of this.clientCallbacks.entries()) {
        if (callbacks === options) {
          this.clientCallbacks.delete(clientId)
          removedClientId = clientId
          break
        }
      }

      // Remove listeners
      if (options.onMarketData) {
        this.removeListener('marketData', options.onMarketData)
      }
      if (options.onMarketDataBatch) {
        this.removeListener('marketDataBatch', options.onMarketDataBatch)
      }

      console.log(`📊 WebSocketManager: Client ${removedClientId} removed (Total: ${this.clientCount})`)

      // Disconnect if no clients with grace period
      if (this.clientCount === 0) {
        console.log('⏳ WebSocketManager: No clients remaining, scheduling disconnect')
        setTimeout(() => {
          if (this.clientCount === 0) {
            console.log('🔌 WebSocketManager: Disconnecting due to no clients')
            this.disconnect()
          }
        }, 5000) // 5 second grace period
      }
    }
  }

  /**
   * Add event listener
   */
  private addListener(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    
    const eventListeners = this.listeners.get(event)!
    if (eventListeners.size >= WEBSOCKET_CONFIG.MAX_LISTENERS_PER_EVENT) {
      console.warn(`⚠️ Maximum listeners reached for event: ${event}`)
      return
    }
    
    eventListeners.add(listener)
  }

  /**
   * Remove event listener
   */
  private removeListener(event: string, listener: Function): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.delete(listener)
      if (eventListeners.size === 0) {
        this.listeners.delete(event)
      }
    }
  }

  /**
   * Notify all listeners for an event
   */
  private notifyListeners(event: string, data: any): void {
    const eventListeners = this.listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`Error in ${event} listener:`, error)
        }
      })
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.socket && this.socket.connected) {
        this.socket.emit('ping')
      }
    }, WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL)
  }

  /**
   * Stop heartbeat
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  /**
   * Start cleanup interval
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, WEBSOCKET_CONFIG.CLEANUP_INTERVAL)
  }

  /**
   * Cleanup stale listeners and connections
   */
  private cleanup(): void {
    // Remove empty listener sets
    const eventsToDelete: string[] = []
    this.listeners.forEach((listeners, event) => {
      if (listeners.size === 0) {
        eventsToDelete.push(event)
      }
    })

    eventsToDelete.forEach(event => {
      this.listeners.delete(event)
    })

    // Log stats
    console.log('📊 WebSocket Stats:', this.getStats())
  }

  /**
   * Disconnect WebSocket with enhanced cleanup
   */
  disconnect(): void {
    console.log('🔌 WebSocketManager: Disconnecting...')

    this.stopHeartbeat()

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    if (this.socket) {
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
    }

    this.listeners.clear()
    this.clientCallbacks.clear()
    this.clientCount = 0
    this.stats.connected = false
    this.stats.clients = 0
    this.stats.connectionId = null
    this.isConnecting = false

    console.log('✅ WebSocketManager: Disconnected and cleaned up')
  }

  /**
   * Get connection statistics
   */
  getStats(): ConnectionStats {
    return { ...this.stats }
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false
  }

  /**
   * Emit event to server
   */
  emit(event: string, data?: any): void {
    if (this.socket && this.socket.connected) {
      this.socket.emit(event, data)
    } else {
      console.warn(`Cannot emit ${event}: WebSocket not connected`)
    }
  }
}

// Export singleton instance and helper functions
export const websocketManager = WebSocketManager.getInstance()

export const createWebSocketConnection = (options: WebSocketOptions = {}) => {
  return websocketManager.connect(options)
}

export const removeWebSocketConnection = (options: WebSocketOptions = {}) => {
  websocketManager.removeClient(options)
}

export const getWebSocketStats = () => {
  return websocketManager.getStats()
}

export const isWebSocketConnected = () => {
  return websocketManager.isConnected()
}

export const emitWebSocketEvent = (event: string, data?: any) => {
  websocketManager.emit(event, data)
}

// Export the WebSocketManager class for direct usage
export { WebSocketManager }

// Also export as default for easier importing
export default WebSocketManager
