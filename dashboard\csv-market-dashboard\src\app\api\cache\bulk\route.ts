/**
 * API Route for Bulk Market Data Cache Operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { cacheBulkMarketData, getBulkMarketData } from '@/lib/redis-client';
import { getFallbackCache } from '@/lib/fallback-cache';

export async function GET() {
  try {
    // Try Redis first
    let cachedData = await getBulkMarketData();
    
    // If Redis fails, try fallback cache
    if (!cachedData || cachedData.length === 0) {
      const fallbackCache = getFallbackCache();
      cachedData = fallbackCache.get('market_data:bulk') || [];
    }
    
    return NextResponse.json({
      success: true,
      data: cachedData,
      count: cachedData.length,
      source: cachedData.length > 0 ? 'cache' : 'none'
    });
  } catch (error) {
    console.error('❌ API: Failed to get bulk market data from cache:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get cached data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    if (!Array.isArray(data)) {
      return NextResponse.json(
        { success: false, error: 'Data must be an array' },
        { status: 400 }
      );
    }

    // Try Redis first
    let redisSuccess = false;
    try {
      redisSuccess = await cacheBulkMarketData(data);
    } catch (error) {
      console.warn('⚠️ Redis cache failed, using fallback cache');
    }
    
    // Always cache in fallback cache as backup
    const fallbackCache = getFallbackCache();
    fallbackCache.set('market_data:bulk', data, 600);
    
    return NextResponse.json({
      success: true,
      message: `Cached ${data.length} market data items`,
      redis: redisSuccess,
      fallback: true
    });
  } catch (error) {
    console.error('❌ API: Failed to cache bulk market data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to cache data' },
      { status: 500 }
    );
  }
}
