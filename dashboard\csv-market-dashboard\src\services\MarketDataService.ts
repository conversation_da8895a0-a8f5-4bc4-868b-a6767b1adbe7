// ============================================================================
// MARKET DATA SERVICE - WebSocket connection to Dhan API using CSV instruments
// ============================================================================

import WebSocket from 'ws';
import { EventEmitter } from 'events';
import { Instrument, MarketData, SubscriptionRequest, IMarketDataService } from '@/types';
import { getBulkMarketData, cacheBulkMarketData, cacheMarketDataEntry } from '@/lib/redis-client';

// Exchange segment mapping for Dhan API
const EXCHANGE_SEGMENTS = {
  'NSE_EQ': 1,
  'NSE_FNO': 2,
  'BSE_EQ': 3,
  'BSE_FNO': 4,
  'MCX_COMM': 5,
  'IDX_I': 6,
} as const;

// Subscription types for Dhan API
const SUBSCRIPTION_TYPES = {
  ticker: 15,
  quote: 17,
  full: 21,
} as const;

export class MarketDataService extends EventEmitter implements IMarketDataService {
  private ws: WebSocket | null = null;
  private isConnected: boolean = false;
  private accessToken: string;
  private clientId: string;
  private subscriptionType: string;
  private subscribedInstruments: Set<string> = new Set();
  private marketData: Map<string, MarketData> = new Map();
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 5000;

  // Instrument lookup cache for fast binary data parsing
  private instrumentLookup: Map<string, Instrument> = new Map();

  constructor(accessToken?: string, clientId?: string, subscriptionType: string = 'quote') {
    super();
    this.accessToken = accessToken || process.env.ACCESS_TOKEN || '';
    this.clientId = clientId || process.env.CLIENT_ID || '';
    this.subscriptionType = subscriptionType;

    if (!this.accessToken || !this.clientId) {
      console.warn('⚠️ ACCESS_TOKEN and CLIENT_ID not provided - market data will be mocked');
    }

    // Initialize Redis connection for server-side caching
    this.initializeRedis();
  }

  /**
   * Initialize Redis connection for server-side caching
   */
  private async initializeRedis(): Promise<void> {
    try {
      console.log('✅ MarketDataService: Redis cache initialized');

      // Load cached market data on startup
      await this.loadCachedMarketData();
    } catch (error) {
      console.error('❌ MarketDataService: Failed to initialize Redis:', error);
    }
  }

  /**
   * Load cached market data from Redis on startup
   */
  private async loadCachedMarketData(): Promise<void> {
    try {
      const cachedData = await getBulkMarketData();
      if (cachedData && cachedData.length > 0) {
        cachedData.forEach((data: MarketData) => {
          this.marketData.set(data.securityId, data);
        });
        console.log(`📖 MarketDataService: Loaded ${cachedData.length} cached market data entries from Redis`);
      }
    } catch (error) {
      console.error('❌ MarketDataService: Failed to load cached market data:', error);
    }
  }

  /**
   * Cache individual market data entry to Redis
   */
  private async cacheMarketDataToRedis(marketData: MarketData): Promise<void> {
    try {
      // Cache individual entry
      await cacheMarketDataEntry(marketData.securityId, marketData, 600); // 10 minutes TTL

      // Periodically cache bulk data (every 100 updates to avoid performance issues)
      if (this.marketData.size % 100 === 0) {
        const allData = Array.from(this.marketData.values());
        await cacheBulkMarketData(allData);
      }
    } catch (error) {
      // Don't log every cache error to avoid spam, just log occasionally
      if (Math.random() < 0.01) { // Log 1% of cache errors
        console.error('❌ MarketDataService: Failed to cache market data to Redis:', error);
      }
    }
  }

  /**
   * Connect to Dhan WebSocket feed
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      console.log('Already connected to market feed');
      return;
    }

    try {
      this.reconnectAttempts++;
      console.log(`🔌 Connecting to Dhan market feed (attempt ${this.reconnectAttempts})`);

      // Check if we have valid credentials
      if (!this.accessToken || !this.clientId) {
        console.log('📊 No credentials provided - starting in mock mode');
        this.startMockDataMode();
        return;
      }

      // ✅ FIXED: Use correct WebSocket URL (from working reference)
      const wsUrl = `wss://api-feed.dhan.co?version=2&token=${encodeURIComponent(this.accessToken)}&clientId=${encodeURIComponent(this.clientId)}&authType=2`;

      this.ws = new WebSocket(wsUrl, {
        headers: {
          'User-Agent': 'CSV-Market-Dashboard/1.0',
        },
        handshakeTimeout: 10000,
      });

      this.setupWebSocketHandlers();

    } catch (error) {
      console.error('❌ Failed to connect to market feed:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    if (!this.ws) return;

    this.ws.on('open', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('✅ Connected to Dhan live market feed');
      this.emit('connected');
    });

    this.ws.on('message', (data) => {
      // ✅ Enhanced debugging for NIFTY spot
      try {
        let buffer: Buffer;
        if (Buffer.isBuffer(data)) {
          buffer = data;
        } else if (data instanceof ArrayBuffer) {
          buffer = Buffer.from(data);
        } else if (Array.isArray(data)) {
          buffer = Buffer.concat(data);
        } else {
          return;
        }

        if (buffer.length >= 8) {
          const securityId = buffer.readUInt32LE(4).toString();
          if (securityId === '13') {
            console.log('🔍 [DEBUG] NIFTY spot packet received! Length:', buffer.length, 'SecurityId:', securityId);
          }
        }
      } catch (e) {
        // Ignore debug errors
      }

      this.handleMarketData(data);
    });

    this.ws.on('close', (code, reason) => {
      this.isConnected = false;
      console.log(`🔌 WebSocket connection closed: ${code} - ${reason}`);
      this.emit('disconnected');
      
      // Attempt reconnection
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => {
          this.connect();
        }, this.reconnectDelay);
      }
    });

    this.ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Handle incoming BINARY market data from Dhan WebSocket
   */
  private handleMarketData(data: WebSocket.Data): void {
    try {
      // Convert to Buffer for binary parsing
      let buffer: Buffer;
      if (Buffer.isBuffer(data)) {
        buffer = data;
      } else if (data instanceof ArrayBuffer) {
        buffer = Buffer.from(data);
      } else if (Array.isArray(data)) {
        buffer = Buffer.concat(data);
      } else {
        return; // Skip unknown data types
      }

      // Parse binary data according to Dhan API specification
      const marketData = this.parseBinaryMarketData(buffer);

      if (marketData && (marketData.ltp > 0 || marketData.openInterest !== undefined)) {
        this.marketData.set(marketData.securityId, marketData);
        this.emit('marketData', marketData);

        // Cache individual market data entry to Redis
        this.cacheMarketDataToRedis(marketData);

        // ✅ Enhanced NIFTY spot logging for debugging
        if (marketData.securityId === '13') {
          console.log(`🎯 [NIFTY SPOT] ✅ DATA RECEIVED!`);
          console.log(`   SecurityId: ${marketData.securityId}`);
          console.log(`   Symbol: ${marketData.symbol || '-'}`);
          console.log(`   Exchange: ${marketData.exchange || '-'}`);
          console.log(`   LTP: ₹${marketData.ltp || '-'}`);
          console.log(`   Volume: ${marketData.volume || '-'}`);
          console.log(`   Change: ${marketData.change || '-'}`);
          console.log(`   High: ₹${marketData.high || '-'}`);
          console.log(`   Low: ₹${marketData.low || '-'}`);
          console.log(`   Timestamp: ${new Date().toISOString()}`);
          console.log(`   ----------------------------------------`);
        }
      }
    } catch (error) {
      console.error('❌ Error handling market data:', error);
    }
  }

  /**
   * Parse BINARY market data according to Dhan API specification
   * ✅ FIXED: Enhanced parsing with proper market depth handling
   */
  private parseBinaryMarketData(buffer: Buffer): MarketData | null {
    try {
      if (buffer.length < 8) {
        return null; // Not enough data for header
      }

      // Parse Response Header (8 bytes)
      const responseCode = buffer.readUInt8(0);
      const messageLength = buffer.readUInt16LE(1);
      const exchangeSegment = buffer.readUInt8(3);
      const securityId = buffer.readUInt32LE(4);

      // Find instrument to get symbol and exchange name
      const instrument = this.findInstrumentBySecurityId(securityId, exchangeSegment);
      if (!instrument) {
        return null; // Skip unknown instruments
      }

      const timestamp = Date.now();
      let marketData: any = {
        securityId: securityId.toString(),
        symbol: instrument.symbol || `SEC_${securityId}`,
        exchange: this.getExchangeSegmentName(exchangeSegment),
        exchangeCode: exchangeSegment,
        timestamp,
        // ✅ FIXED: Include instrument metadata for option matching
        expiryDate: instrument.expiryDate ? instrument.expiryDate.toISOString().split('T')[0] : undefined, // Convert Date to YYYY-MM-DD string
        strikePrice: instrument.strikePrice,
        optionType: instrument.optionType,
        instrumentType: instrument.instrumentType
      };

      // ✅ FIXED: Enhanced logging with structured prefixes
      const logPrefix = this.getLogPrefix(instrument.symbol, responseCode);

      // Parse different packet types based on response code
      switch (responseCode) {
        case 2: // Ticker packet (16 bytes total)
          if (buffer.length >= 16) {
            marketData.ltp = buffer.readFloatLE(8);
            marketData.ltt = buffer.readUInt32LE(12);
            console.log(`${logPrefix}[TICKER] LTP: ₹${marketData.ltp}`);
          }
          break;

        case 4: // Quote packet (50 bytes total)
          if (buffer.length >= 50) {
            marketData.ltp = buffer.readFloatLE(8);
            marketData.ltq = buffer.readUInt16LE(12);
            marketData.ltt = buffer.readUInt32LE(14);
            marketData.atp = buffer.readFloatLE(18);
            marketData.volume = buffer.readUInt32LE(22);
            marketData.totalSellQuantity = buffer.readUInt32LE(26);
            marketData.totalBuyQuantity = buffer.readUInt32LE(30);
            marketData.open = buffer.readFloatLE(34);
            marketData.close = buffer.readFloatLE(38);
            marketData.high = buffer.readFloatLE(42);
            marketData.low = buffer.readFloatLE(46);

            this.calculatePriceChange(marketData);
            console.log(`${logPrefix}[QUOTE] LTP: ₹${marketData.ltp}, Vol: ${marketData.volume}`);
          }
          break;

        case 5: // OI Data packet (12 bytes total)
          if (buffer.length >= 12) {
            marketData.openInterest = buffer.readUInt32LE(8);
            // For OI-only packets, merge with existing data if available
            const existingData = this.marketData.get(marketData.securityId);
            if (existingData) {
              marketData = { ...existingData, ...marketData, timestamp };
            } else {
              // Set default values for OI-only packet
              marketData.ltp = 0;
              marketData.volume = 0;
              marketData.change = 0;
              marketData.changePercent = 0;
            }
            // ✅ Removed verbose OI logging
          }
          break;

        case 6: // Previous close packet (16 bytes total)
          if (buffer.length >= 16) {
            marketData.previousClose = buffer.readFloatLE(8);
            // For previous close packets, merge with existing data if available
            const existingData = this.marketData.get(marketData.securityId);
            if (existingData) {
              marketData = { ...existingData, ...marketData, timestamp };
            } else {
              // Set default values for previous close packet
              marketData.ltp = 0;
              marketData.volume = 0;
              marketData.change = 0;
              marketData.changePercent = 0;
            }
            console.log(`${logPrefix}[CLOSE] Previous Close: ₹${marketData.previousClose}`);
          }
          break;

        case 8: // Full packet (162 bytes total) - ✅ ENHANCED with market depth parsing
          if (buffer.length >= 162) {
            // Basic market data (first 62 bytes)
            marketData.ltp = buffer.readFloatLE(8);
            marketData.ltq = buffer.readUInt16LE(12);
            marketData.ltt = buffer.readUInt32LE(14);
            marketData.atp = buffer.readFloatLE(18);
            marketData.volume = buffer.readUInt32LE(22);
            marketData.totalSellQuantity = buffer.readUInt32LE(26);
            marketData.totalBuyQuantity = buffer.readUInt32LE(30);
            marketData.openInterest = buffer.readUInt32LE(34);
            marketData.open = buffer.readFloatLE(46);
            marketData.close = buffer.readFloatLE(50);
            marketData.high = buffer.readFloatLE(54);
            marketData.low = buffer.readFloatLE(58);

            // ✅ FIXED: Parse Market Depth (bytes 63-162, 5 packets of 20 bytes each)
            marketData.marketDepth = this.parseMarketDepth(buffer, 62);

            // Extract best bid/ask from market depth
            if (marketData.marketDepth && marketData.marketDepth.length > 0) {
              const bestBid = marketData.marketDepth[0].bidPrice;
              const bestAsk = marketData.marketDepth[0].askPrice;
              const bestBidQty = marketData.marketDepth[0].bidQty;
              const bestAskQty = marketData.marketDepth[0].askQty;

              marketData.bid = bestBid > 0 ? bestBid : undefined;
              marketData.ask = bestAsk > 0 ? bestAsk : undefined;
              marketData.bidQty = bestBidQty > 0 ? bestBidQty : undefined;
              marketData.askQty = bestAskQty > 0 ? bestAskQty : undefined;

              // ✅ Removed verbose debug logging
            }

            this.calculatePriceChange(marketData);

            // ✅ Enhanced NIFTY spot logging for debugging
            if (instrument.symbol === 'NIFTY' || marketData.securityId === '13') {
              console.log(`[SPOT] NIFTY (ID: ${marketData.securityId}) | LTP: ₹${marketData.ltp}, Vol: ${marketData.volume}, Change: ${marketData.change || '-'}%`);
            }
          }
          break;

        default:
          return null; // Unknown response code
      }

      // Return if we have valid price data OR open interest data (for derivatives)
      return (marketData.ltp > 0 || marketData.openInterest !== undefined) ? marketData as MarketData : null;
    } catch (error) {
      console.error('❌ Error parsing binary market data:', error);
      return null;
    }
  }

  /**
   * ✅ FIXED: Parse Market Depth from full packet (5 levels of bid/ask)
   * According to Dhan docs: Each level is 20 bytes with structure:
   * Bytes 1-4: Bid Quantity (int32)
   * Bytes 5-8: Ask Quantity (int32)
   * Bytes 9-10: No. of Bid Orders (int16)
   * Bytes 11-12: No. of Ask Orders (int16)
   * Bytes 13-16: Bid Price (float32)
   * Bytes 17-20: Ask Price (float32)
   */
  private parseMarketDepth(buffer: Buffer, startOffset: number): Array<{
    level: number;
    bidPrice: number;
    bidQty: number;
    askPrice: number;
    askQty: number;
    bidOrders?: number;
    askOrders?: number;
  }> | null {
    try {
      const marketDepth = [];

      for (let level = 0; level < 5; level++) {
        const offset = startOffset + (level * 20);

        if (buffer.length < offset + 20) {
          break; // Not enough data for this level
        }

        // ✅ FIXED: Correct byte order according to Dhan documentation
        const bidQty = buffer.readInt32LE(offset);           // Bytes 1-4
        const askQty = buffer.readInt32LE(offset + 4);       // Bytes 5-8
        const bidOrders = buffer.readInt16LE(offset + 8);    // Bytes 9-10
        const askOrders = buffer.readInt16LE(offset + 10);   // Bytes 11-12
        const bidPrice = buffer.readFloatLE(offset + 12);    // Bytes 13-16
        const askPrice = buffer.readFloatLE(offset + 16);    // Bytes 17-20

        // Only add levels with valid data
        if (bidPrice > 0 || askPrice > 0 || bidQty > 0 || askQty > 0) {
          marketDepth.push({
            level: level + 1,
            bidPrice: bidPrice > 0 ? bidPrice : 0,
            bidQty: bidQty > 0 ? bidQty : 0,
            askPrice: askPrice > 0 ? askPrice : 0,
            askQty: askQty > 0 ? askQty : 0,
            bidOrders: bidOrders > 0 ? bidOrders : 0,
            askOrders: askOrders > 0 ? askOrders : 0
          });
        }
      }

      return marketDepth.length > 0 ? marketDepth : null;
    } catch (error) {
      console.error('❌ Error parsing market depth:', error);
      return null;
    }
  }

  /**
   * ✅ NEW: Generate structured log prefix for better debugging
   */
  private getLogPrefix(symbol: string, responseCode: number): string {
    const codeMap: { [key: number]: string } = {
      2: 'TICKER',
      4: 'QUOTE',
      5: 'OI',
      6: 'CLOSE',
      8: 'FULL'
    };

    const packetType = codeMap[responseCode] || `CODE_${responseCode}`;

    // Special prefixes for important instruments
    if (symbol === 'NIFTY') {
      return `[SPOT]`;
    } else if (symbol.includes('NIFTY-') && symbol.includes('-CE')) {
      return `[CALL]`;
    } else if (symbol.includes('NIFTY-') && symbol.includes('-PE')) {
      return `[PUT]`;
    } else if (symbol.includes('NIFTY-') && symbol.includes('-FUT')) {
      return `[FUTURE]`;
    }

    return `[${packetType}]`;
  }

  /**
   * Calculate price change and percentage
   */
  private calculatePriceChange(marketData: any): void {
    if (marketData.ltp && marketData.close && marketData.close !== marketData.ltp) {
      marketData.change = marketData.ltp - marketData.close;
      marketData.changePercent = (marketData.change / marketData.close) * 100;
    }
  }

  /**
   * Find instrument by security ID and exchange segment using lookup cache
   */
  private findInstrumentBySecurityId(securityId: number, exchangeSegment: number): Instrument | null {
    const lookupKey = `${exchangeSegment}-${securityId}`;
    return this.instrumentLookup.get(lookupKey) || null;
  }

  /**
   * Get exchange segment name from code
   */
  private getExchangeSegmentName(code: number): string {
    const exchangeMap: { [key: number]: string } = {
      0: 'IDX_I',
      1: 'NSE_EQ',
      2: 'NSE_FNO',
      3: 'NSE_CURRENCY',
      4: 'BSE_EQ',
      5: 'MCX_COMM',
      7: 'BSE_CURRENCY',
      8: 'BSE_FNO',
    };
    return exchangeMap[code] || `Unknown(${code})`;
  }

  /**
   * Subscribe to instruments (COPIED FROM WORKING PROJECT)
   */
  subscribe(instruments: Instrument[]): void {
    if (!this.isConnected) {
      console.warn('⚠️ Not connected to market feed - cannot subscribe');
      return;
    }

    // Build instrument lookup cache for binary parsing
    instruments.forEach(instrument => {
      if (!this.subscribedInstruments.has(instrument.securityId)) {
        this.subscribedInstruments.add(instrument.securityId);

        // Build instrument lookup cache for binary parsing
        const lookupKey = `${instrument.exchangeCode}-${instrument.securityId}`;
        this.instrumentLookup.set(lookupKey, instrument);
      }
    });

    // Subscribe in batches to avoid overwhelming the server (COPIED FROM WORKING PROJECT)
    const batchSize = 100;
    const batches: Instrument[][] = [];

    for (let i = 0; i < instruments.length; i += batchSize) {
      batches.push(instruments.slice(i, i + batchSize));
    }

    console.log('📦 Subscription batches prepared', {
      totalBatches: batches.length,
      batchSize,
      totalInstruments: instruments.length,
    });

    // Subscribe to batches with delay (COPIED FROM WORKING PROJECT)
    for (let i = 0; i < batches.length; i++) {
      setTimeout(() => {
        this.subscribeToBatch(batches[i]);
      }, i * 200); // 200ms delay between batches (like working code)
    }
  }

  /**
   * Subscribe to a batch of instruments (COPIED FROM WORKING PROJECT)
   */
  private subscribeToBatch(instruments: Instrument[]): void {
    if (!this.ws || !this.isConnected) {
      console.warn('Cannot subscribe - WebSocket not open');
      return;
    }

    const requestCode = SUBSCRIPTION_TYPES[this.subscriptionType as keyof typeof SUBSCRIPTION_TYPES];

    // COPIED FROM WORKING PROJECT: Use STRING format
    const subscriptionMessage = {
      RequestCode: requestCode,
      InstrumentCount: instruments.length,
      InstrumentList: instruments.map((inst) => ({
        ExchangeSegment: inst.exchange, // ✅ Use STRING exchange name (like working implementation)
        SecurityId: inst.securityId.toString(), // ✅ Use STRING security ID
      })),
    };

    try {
      // ✅ Enhanced debugging for NIFTY spot subscription
      if (instruments.some(i => i.securityId === '13')) {
        console.log('🔍 [DEBUG] NIFTY spot subscription message:', JSON.stringify(subscriptionMessage, null, 2));
      }

      this.ws.send(JSON.stringify(subscriptionMessage));
      console.log('✅ Subscribed to instrument batch', {
        count: instruments.length,
        requestCode,
        sample: instruments.slice(0, 3).map((i) => ({
          symbol: i.symbol,
          exchange: i.exchange,
          securityId: i.securityId,
        })),
      });
    } catch (error) {
      console.error('❌ Failed to send subscription', {
        error: (error as Error).message,
        count: instruments.length,
      });
    }
  }

  /**
   * Unsubscribe from instruments
   */
  unsubscribe(instruments: Instrument[]): void {
    if (!this.isConnected) {
      return;
    }

    instruments.forEach(instrument => {
      if (this.subscribedInstruments.has(instrument.securityId)) {
        this.subscribedInstruments.delete(instrument.securityId);
        this.marketData.delete(instrument.securityId);

        // Remove from instrument lookup cache
        const exchangeSegment = this.getCorrectExchangeSegment(instrument);
        const lookupKey = `${exchangeSegment}-${instrument.securityId}`;
        this.instrumentLookup.delete(lookupKey);
      }
    });

    console.log(`📡 Unsubscribed from ${instruments.length} instruments`);
  }

  /**
   * Get correct exchange segment for subscription (FIXED: Use NSE_FNO for NSE derivatives)
   */
  private getCorrectExchangeSegment(instrument: Instrument): number {
    // For NSE derivatives (OPTIDX, FUTIDX, OPTSTK, FUTSTK), always use NSE_FNO (2)
    if (['OPTIDX', 'FUTIDX', 'OPTSTK', 'FUTSTK'].includes(instrument.instrumentType)) {
      console.log(`🎯 Using NSE_FNO (2) for ${instrument.instrumentType}: ${instrument.symbol}`);
      return 2; // NSE_FNO
    }

    // For other instruments, use the original exchange code
    return instrument.exchangeCode;
  }



  /**
   * Get market data for a specific security
   */
  getMarketData(securityId: string): MarketData | null {
    return this.marketData.get(securityId) || null;
  }

  /**
   * Get all market data
   */
  getAllMarketData(): Map<string, MarketData> {
    return new Map(this.marketData);
  }

  /**
   * Check if connected
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Get connection status as property
   */
  get connected(): boolean {
    return this.isConnected;
  }

  /**
   * Get subscription count
   */
  getSubscriptionCount(): number {
    return this.subscribedInstruments.size;
  }

  /**
   * Disconnect from market feed
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    this.subscribedInstruments.clear();
    this.marketData.clear();
    console.log('🔌 Disconnected from market feed');
  }

  /**
   * Handle connection errors
   */
  private handleConnectionError(): void {
    this.isConnected = false;
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      console.log(`🔄 Retrying connection in ${this.reconnectDelay / 1000} seconds...`);
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay);
    } else {
      console.log('❌ Max reconnection attempts reached - starting mock mode');
      this.startMockDataMode();
    }
  }

  /**
   * Start mock data mode for testing
   */
  private startMockDataMode(): void {
    this.isConnected = true;
    console.log('🎭 Starting mock market data mode');
    this.emit('connected');

    // Generate mock data every 2 seconds
    setInterval(() => {
      this.generateMockData();
    }, 2000);
  }

  /**
   * Generate mock market data with realistic bid/ask values
   */
  private generateMockData(): void {
    this.subscribedInstruments.forEach(securityId => {
      const existing = this.marketData.get(securityId);
      const basePrice = existing?.ltp || 100 + Math.random() * 1000;

      // Generate realistic price movement
      const changePercent = (Math.random() - 0.5) * 10; // ±5%
      const change = basePrice * (changePercent / 100);
      const newPrice = Math.max(0.01, basePrice + change);

      // ✅ Generate realistic bid/ask spread (0.5% to 2% spread)
      const spreadPercent = 0.005 + Math.random() * 0.015; // 0.5% to 2%
      const spread = newPrice * spreadPercent;
      const bidPrice = parseFloat((newPrice - spread / 2).toFixed(2));
      const askPrice = parseFloat((newPrice + spread / 2).toFixed(2));

      // Generate realistic quantities
      const bidQty = Math.floor(Math.random() * 1000) + 50;
      const askQty = Math.floor(Math.random() * 1000) + 50;

      // Generate 5-level market depth
      const marketDepth = [];
      for (let level = 1; level <= 5; level++) {
        const levelSpread = spread * level * 0.5;
        marketDepth.push({
          level,
          bidPrice: parseFloat((bidPrice - levelSpread * (level - 1) * 0.1).toFixed(2)),
          bidQty: Math.floor(bidQty / level) + Math.floor(Math.random() * 100),
          askPrice: parseFloat((askPrice + levelSpread * (level - 1) * 0.1).toFixed(2)),
          askQty: Math.floor(askQty / level) + Math.floor(Math.random() * 100),
          bidOrders: Math.floor(Math.random() * 20) + 1,
          askOrders: Math.floor(Math.random() * 20) + 1
        });
      }

      // Find instrument data for this security ID
      const instrument = Array.from(this.instrumentLookup.values()).find(inst => inst.securityId === securityId);

      const mockData: MarketData = {
        securityId,
        symbol: instrument?.symbol || `MOCK_${securityId}`,
        exchange: instrument?.exchange || 'MOCK',
        ltp: parseFloat(newPrice.toFixed(2)),
        change: parseFloat(change.toFixed(2)),
        changePercent: parseFloat(changePercent.toFixed(2)),
        volume: Math.floor(Math.random() * 1000000),
        high: parseFloat((newPrice * (1 + Math.random() * 0.05)).toFixed(2)),
        low: parseFloat((newPrice * (1 - Math.random() * 0.05)).toFixed(2)),
        open: parseFloat((basePrice * (1 + (Math.random() - 0.5) * 0.02)).toFixed(2)),
        close: basePrice,
        timestamp: Date.now(),
        // ✅ Add bid/ask data
        bid: bidPrice,
        ask: askPrice,
        bidQty: bidQty,
        askQty: askQty,
        marketDepth: marketDepth,
        openInterest: Math.floor(Math.random() * 100000) + 1000,
        // ✅ FIXED: Include instrument metadata for option matching
        expiryDate: instrument?.expiryDate ? instrument.expiryDate.toISOString().split('T')[0] : undefined,
        strikePrice: instrument?.strikePrice,
        optionType: instrument?.optionType,
        instrumentType: instrument?.instrumentType
      };

      this.marketData.set(securityId, mockData);
      this.emit('marketData', mockData);
    });
  }
}

// Export singleton instance with FULL subscription type to get OI data
export const marketDataService = new MarketDataService(undefined, undefined, 'full');
