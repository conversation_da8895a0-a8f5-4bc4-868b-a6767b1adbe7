"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[869],{8705:function(t,e,n){n.d(e,{Hh:function(){return s},Hz:function(){return c},ej:function(){return i}});var o=n(257);let c={BASE_URL:o.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},s={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},i={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:52428800,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(o.env.PORT||"8080"),parseInt(o.env.NEXT_PORT||"3000"),o.env.LOG_LEVEL,o.env.ENABLE_METRICS},3448:function(t,e,n){n.d(e,{A1:function(){return o}});let o={price:t=>!t||t<=0?"-":"₹".concat(t.toFixed(2)),number:t=>null==t?"-":t.toLocaleString(),percentage:t=>t&&0!==t?"".concat(t>=0?"+":"").concat(t.toFixed(2),"%"):"-",change:(t,e)=>{if(!t||0===t)return"-";let n=t>0?"+":"",o=e?" (".concat(n).concat(e.toFixed(2),"%)"):"";return"".concat(n).concat(t.toFixed(2)).concat(o)},volume:t=>null==t?"-":t>=1e7?"".concat((t/1e7).toFixed(2)," Cr"):t>=1e5?"".concat((t/1e5).toFixed(2)," L"):t>=1e3?"".concat((t/1e3).toFixed(2)," K"):t.toString(),time:t=>t?new Date(t).toLocaleTimeString():"-",date:t=>t?new Date(t).toLocaleDateString():"-",bidAsk:(t,e)=>!t||t<=0?"-":"₹".concat(t.toFixed(2)).concat(e?" (".concat(e,")"):"")}},5052:function(t,e,n){var o=n(8680),c=n(8705);class s{static getInstance(){return s.instance||(s.instance=new s,console.log("\uD83D\uDD27 WebSocketManager: New singleton instance created")),s.instance}async connect(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=this.generateClientId();if(console.log("\uD83D\uDD0C WebSocketManager: Client ".concat(e," requesting connection")),this.clientCallbacks.set(e,t),this.socket&&this.socket.connected)return console.log("✅ WebSocketManager: Reusing existing connection for client ".concat(e)),this.addClient(t,e),this.socket;if(this.isConnecting)return console.log("⏳ WebSocketManager: Connection in progress, waiting for client ".concat(e)),new Promise((n,o)=>{let c=()=>{this.socket&&this.socket.connected?(console.log("✅ WebSocketManager: Connection ready for waiting client ".concat(e)),this.addClient(t,e),n(this.socket)):this.isConnecting?setTimeout(c,100):(console.log("❌ WebSocketManager: Connection failed for waiting client ".concat(e)),o(Error("Connection failed")))};c()});console.log("\uD83D\uDE80 WebSocketManager: Creating new connection for client ".concat(e)),this.isConnecting=!0;try{return await this.createConnection(t),this.addClient(t,e),this.socket}catch(t){throw this.isConnecting=!1,this.clientCallbacks.delete(e),console.error("❌ WebSocketManager: Connection failed for client ".concat(e,":"),t),t}}generateClientId(){return"client_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}async createConnection(t){return new Promise((t,e)=>{let n=c.Hz.BASE_URL;console.log("\uD83D\uDD0C WebSocketManager: Creating connection to ".concat(n)),this.socket&&(console.log("\uD83E\uDDF9 WebSocketManager: Cleaning up existing connection"),this.socket.removeAllListeners(),this.socket.disconnect()),this.socket=(0,o.io)(n,{transports:["websocket","polling"],upgrade:!0,rememberUpgrade:!1,timeout:c.Hh.CONNECTION_TIMEOUT,forceNew:!1,reconnection:!0,reconnectionAttempts:c.Hh.MAX_RECONNECT_ATTEMPTS,reconnectionDelay:c.Hh.RECONNECT_INTERVAL,reconnectionDelayMax:4*c.Hh.RECONNECT_INTERVAL,randomizationFactor:.5,autoConnect:!0}),this.socket.on("connect",()=>{var e;console.log("✅ WebSocketManager: Connected successfully"),this.isConnecting=!1,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.reconnectAttempts=0,this.stats.connectionId=(null===(e=this.socket)||void 0===e?void 0:e.id)||null,this.startHeartbeat(),this.notifyAllClients("onConnect"),t()}),this.socket.on("disconnect",t=>{console.log("❌ WebSocketManager: Disconnected - ".concat(t)),this.stats.connected=!1,this.stats.connectionId=null,this.stopHeartbeat(),this.notifyAllClients("onDisconnect",t),"io client disconnect"!==t&&"client namespace disconnect"!==t&&this.scheduleReconnection()}),this.socket.on("connect_error",t=>{console.error("\uD83D\uDD25 WebSocketManager: Connection error:",t.message),this.stats.errors++,this.isConnecting=!1,this.notifyAllClients("onError",t),e(t)}),this.socket.on("reconnect",t=>{var e;console.log("\uD83D\uDD04 WebSocketManager: Reconnected after ".concat(t," attempts")),this.stats.reconnectAttempts=t,this.stats.connected=!0,this.stats.lastConnected=new Date,this.stats.connectionId=(null===(e=this.socket)||void 0===e?void 0:e.id)||null,this.notifyAllClients("onReconnect",t)}),this.socket.on("reconnect_attempt",t=>{console.log("\uD83D\uDD04 WebSocketManager: Reconnection attempt ".concat(t)),this.stats.reconnectAttempts=t}),this.socket.on("reconnect_failed",()=>{console.error("\uD83D\uDCA5 WebSocketManager: Reconnection failed"),this.stats.connected=!1,this.isConnecting=!1,this.stats.connectionId=null}),this.socket.on("marketData",t=>{this.stats.totalMessages++,this.notifyListeners("marketData",t)}),this.socket.on("marketDataBatch",t=>{this.stats.totalMessages+=t.length,this.notifyListeners("marketDataBatch",t)}),this.startCleanupInterval(),setTimeout(()=>{this.isConnecting&&(this.isConnecting=!1,e(Error("Connection timeout")))},c.Hh.CONNECTION_TIMEOUT)})}addClient(t,e){this.clientCount++,this.stats.clients=this.clientCount,t.onMarketData&&this.addListener("marketData",t.onMarketData),t.onMarketDataBatch&&this.addListener("marketDataBatch",t.onMarketDataBatch),console.log("\uD83D\uDCCA WebSocketManager: Client ".concat(e," added (Total: ").concat(this.clientCount,")"))}notifyAllClients(t,e){this.clientCallbacks.forEach((n,o)=>{try{var c,s,i,a;switch(t){case"onConnect":null===(c=n.onConnect)||void 0===c||c.call(n);break;case"onDisconnect":null===(s=n.onDisconnect)||void 0===s||s.call(n,e);break;case"onError":null===(i=n.onError)||void 0===i||i.call(n,e);break;case"onReconnect":null===(a=n.onReconnect)||void 0===a||a.call(n,e)}}catch(t){console.error("❌ WebSocketManager: Error notifying client ".concat(o,":"),t)}})}scheduleReconnection(){this.reconnectTimeout&&clearTimeout(this.reconnectTimeout);let t=Math.min(c.Hh.RECONNECT_INTERVAL*Math.pow(2,this.stats.reconnectAttempts),3e4);console.log("\uD83D\uDD04 WebSocketManager: Scheduling reconnection in ".concat(t,"ms")),this.reconnectTimeout=setTimeout(()=>{!this.stats.connected&&this.clientCount>0&&(console.log("\uD83D\uDD04 WebSocketManager: Attempting auto-reconnection"),this.connect().catch(t=>{console.error("❌ WebSocketManager: Auto-reconnection failed:",t)}))},t)}removeClient(t){if(this.clientCount>0){this.clientCount--,this.stats.clients=this.clientCount;let e="unknown";for(let[n,o]of this.clientCallbacks.entries())if(o===t){this.clientCallbacks.delete(n),e=n;break}t.onMarketData&&this.removeListener("marketData",t.onMarketData),t.onMarketDataBatch&&this.removeListener("marketDataBatch",t.onMarketDataBatch),console.log("\uD83D\uDCCA WebSocketManager: Client ".concat(e," removed (Total: ").concat(this.clientCount,")")),0===this.clientCount&&(console.log("⏳ WebSocketManager: No clients remaining, scheduling disconnect"),setTimeout(()=>{0===this.clientCount&&(console.log("\uD83D\uDD0C WebSocketManager: Disconnecting due to no clients"),this.disconnect())},5e3))}}addListener(t,e){this.listeners.has(t)||this.listeners.set(t,new Set);let n=this.listeners.get(t);if(n.size>=c.Hh.MAX_LISTENERS_PER_EVENT){console.warn("⚠️ Maximum listeners reached for event: ".concat(t));return}n.add(e)}removeListener(t,e){let n=this.listeners.get(t);n&&(n.delete(e),0===n.size&&this.listeners.delete(t))}notifyListeners(t,e){let n=this.listeners.get(t);n&&n.forEach(n=>{try{n(e)}catch(e){console.error("Error in ".concat(t," listener:"),e)}})}startHeartbeat(){this.stopHeartbeat(),this.heartbeatInterval=setInterval(()=>{this.socket&&this.socket.connected&&this.socket.emit("ping")},c.Hh.HEARTBEAT_INTERVAL)}stopHeartbeat(){this.heartbeatInterval&&(clearInterval(this.heartbeatInterval),this.heartbeatInterval=null)}startCleanupInterval(){this.cleanupInterval=setInterval(()=>{this.cleanup()},c.Hh.CLEANUP_INTERVAL)}cleanup(){let t=[];this.listeners.forEach((e,n)=>{0===e.size&&t.push(n)}),t.forEach(t=>{this.listeners.delete(t)}),console.log("\uD83D\uDCCA WebSocket Stats:",this.getStats())}disconnect(){console.log("\uD83D\uDD0C WebSocketManager: Disconnecting..."),this.stopHeartbeat(),this.cleanupInterval&&(clearInterval(this.cleanupInterval),this.cleanupInterval=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.socket&&(this.socket.removeAllListeners(),this.socket.disconnect(),this.socket=null),this.listeners.clear(),this.clientCallbacks.clear(),this.clientCount=0,this.stats.connected=!1,this.stats.clients=0,this.stats.connectionId=null,this.isConnecting=!1,console.log("✅ WebSocketManager: Disconnected and cleaned up")}getStats(){return{...this.stats}}isConnected(){var t;return(null===(t=this.socket)||void 0===t?void 0:t.connected)||!1}emit(t,e){this.socket&&this.socket.connected?this.socket.emit(t,e):console.warn("Cannot emit ".concat(t,": WebSocket not connected"))}constructor(){this.socket=null,this.isConnecting=!1,this.clientCount=0,this.listeners=new Map,this.clientCallbacks=new Map,this.stats={connected:!1,clients:0,reconnectAttempts:0,lastConnected:null,totalMessages:0,errors:0,connectionId:null},this.heartbeatInterval=null,this.cleanupInterval=null,this.reconnectTimeout=null,console.log("\uD83D\uDD27 WebSocketManager: Constructor called")}}s.instance=null,s.getInstance(),e.ZP=s}}]);