/**
 * Enhanced React Hook for WebSocket Connection Management with Data Caching
 * FIXED: Singleton connection sharing and data persistence
 */

import { useEffect, useRef, useState, useCallback } from 'react'
import WebSocketManager from '@/lib/websocket-manager'
import { dataCache, cacheHelpers, MARKET_DATA_CACHE_KEYS } from '@/lib/data-cache'
import type { Socket } from 'socket.io-client'

interface UseWebSocketOptions {
  onConnect?: () => void
  onDisconnect?: (reason: string) => void
  onError?: (error: Error) => void
  onReconnect?: (attemptNumber: number) => void
  onMarketData?: (data: any) => void
  onMarketDataBatch?: (data: any[]) => void
  autoConnect?: boolean
  enableCaching?: boolean
  cacheKey?: string
}

interface UseWebSocketReturn {
  socket: Socket | null
  isConnected: boolean
  isConnecting: boolean
  error: string | null
  stats: any
  connect: () => Promise<void>
  disconnect: () => void
  emit: (event: string, data?: any) => void
}

export function useWebSocket(options: UseWebSocketOptions = {}): UseWebSocketReturn {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<any>({})

  const optionsRef = useRef(options)
  const mountedRef = useRef(true)
  const wsManager = useRef(WebSocketManager.getInstance())

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options
  }, [options])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (socket) {
        wsManager.current.removeClient(optionsRef.current)
      }
    }
  }, [socket])

  // Connect function
  const connect = useCallback(async () => {
    if (isConnecting || isConnected) return

    setIsConnecting(true)
    setError(null)

    try {
      const socketInstance = await wsManager.current.connect({
        ...optionsRef.current,
        onConnect: () => {
          if (!mountedRef.current) return
          setIsConnected(true)
          setIsConnecting(false)
          setError(null)
          setStats(wsManager.current.getStats())
          optionsRef.current.onConnect?.()
        },
        onDisconnect: (reason) => {
          if (!mountedRef.current) return
          setIsConnected(false)
          setError(`Disconnected: ${reason}`)
          setStats(wsManager.current.getStats())
          optionsRef.current.onDisconnect?.(reason)
        },
        onError: (err) => {
          if (!mountedRef.current) return
          setIsConnecting(false)
          setError(err.message)
          setStats(wsManager.current.getStats())
          optionsRef.current.onError?.(err)
        },
        onReconnect: (attemptNumber) => {
          if (!mountedRef.current) return
          setIsConnected(true)
          setError(null)
          setStats(wsManager.current.getStats())
          optionsRef.current.onReconnect?.(attemptNumber)
        },
      })

      if (mountedRef.current) {
        setSocket(socketInstance)
      }
    } catch (err) {
      if (mountedRef.current) {
        setIsConnecting(false)
        setError(err instanceof Error ? err.message : 'Connection failed')
      }
    }
  }, [isConnecting, isConnected])

  // Disconnect function
  const disconnect = useCallback(() => {
    if (socket) {
      wsManager.current.removeClient(optionsRef.current)
      setSocket(null)
      setIsConnected(false)
      setIsConnecting(false)
      setError(null)
      setStats(wsManager.current.getStats())
    }
  }, [socket])

  // Emit function
  const emit = useCallback((event: string, data?: any) => {
    if (socket && isConnected) {
      socket.emit(event, data)
    } else {
      console.warn(`Cannot emit ${event}: WebSocket not connected`)
    }
  }, [socket, isConnected])

  // Auto-connect on mount
  useEffect(() => {
    if (options.autoConnect !== false) {
      connect()
    }
  }, [connect, options.autoConnect])

  // Update stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (mountedRef.current) {
        setStats(wsManager.current.getStats())
      }
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  return {
    socket,
    isConnected,
    isConnecting,
    error,
    stats,
    connect,
    disconnect,
    emit,
  }
}

// Enhanced specialized hook for market data with caching
export function useMarketData() {
  const [marketData, setMarketData] = useState<Map<string, any>>(new Map())
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [cacheLoaded, setCacheLoaded] = useState(false)

  // Load cached data on mount
  useEffect(() => {
    const loadCachedData = async () => {
      try {
        console.log('📖 useMarketData: Loading cached market data...')

        // Load cached market data
        const cachedData = await cacheHelpers.getCachedMarketData(MARKET_DATA_CACHE_KEYS.MARKET_DATA)

        if (cachedData && Array.isArray(cachedData)) {
          const dataMap = new Map()
          cachedData.forEach((item: any) => {
            if (item.securityId) {
              dataMap.set(item.securityId, item)
            }
          })

          setMarketData(dataMap)
          setLastUpdate(new Date())
          setCacheLoaded(true)
          console.log(`✅ useMarketData: Loaded ${cachedData.length} items from cache`)
        }

        // Load cached NIFTY spot price
        const cachedNiftySpot = await cacheHelpers.getCachedMarketData(MARKET_DATA_CACHE_KEYS.NIFTY_SPOT)
        if (cachedNiftySpot) {
          console.log('✅ useMarketData: Loaded NIFTY spot from cache:', cachedNiftySpot)
        }

      } catch (error) {
        console.error('❌ useMarketData: Failed to load cached data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadCachedData()
  }, [])

  const { isConnected, error, stats } = useWebSocket({
    onMarketData: (data) => {
      setMarketData(prev => {
        const newMap = new Map(prev)
        newMap.set(data.securityId, data)

        // Cache individual market data updates
        if (data.securityId === '13') {
          // Cache NIFTY spot separately
          cacheHelpers.cacheMarketData(MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, data)
        }

        return newMap
      })
      setLastUpdate(new Date())
    },
    onMarketDataBatch: (dataArray) => {
      setMarketData(prev => {
        const newMap = new Map(prev)
        dataArray.forEach(data => {
          newMap.set(data.securityId, data)
        })

        // Cache the batch data
        cacheHelpers.cacheMarketData(MARKET_DATA_CACHE_KEYS.MARKET_DATA, dataArray)

        return newMap
      })
      setLastUpdate(new Date())
    },
    onConnect: () => {
      console.log('✅ useMarketData: WebSocket connected')
      setIsLoading(false)
    },
    onDisconnect: () => {
      console.log('❌ useMarketData: WebSocket disconnected')
    },
    enableCaching: true,
    cacheKey: MARKET_DATA_CACHE_KEYS.MARKET_DATA
  })

  // Cache market data periodically
  useEffect(() => {
    if (marketData.size > 0) {
      const interval = setInterval(() => {
        const dataArray = Array.from(marketData.values())
        cacheHelpers.cacheMarketData(MARKET_DATA_CACHE_KEYS.MARKET_DATA, dataArray)
      }, 30000) // Cache every 30 seconds

      return () => clearInterval(interval)
    }
  }, [marketData])

  const getMarketData = useCallback((securityId: string) => {
    return marketData.get(securityId)
  }, [marketData])

  const getAllMarketData = useCallback(() => {
    return Array.from(marketData.values())
  }, [marketData])

  const clearMarketData = useCallback(() => {
    setMarketData(new Map())
    setLastUpdate(null)
    // Clear cache as well
    dataCache.remove(MARKET_DATA_CACHE_KEYS.MARKET_DATA, { storage: 'sessionStorage' })
    dataCache.remove(MARKET_DATA_CACHE_KEYS.NIFTY_SPOT, { storage: 'sessionStorage' })
  }, [])

  const refreshFromCache = useCallback(async () => {
    setIsLoading(true)
    try {
      const cachedData = await cacheHelpers.getCachedMarketData(MARKET_DATA_CACHE_KEYS.MARKET_DATA)
      if (cachedData && Array.isArray(cachedData)) {
        const dataMap = new Map()
        cachedData.forEach((item: any) => {
          if (item.securityId) {
            dataMap.set(item.securityId, item)
          }
        })
        setMarketData(dataMap)
        setLastUpdate(new Date())
      }
    } catch (error) {
      console.error('❌ useMarketData: Failed to refresh from cache:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    marketData: getAllMarketData(),
    getMarketData,
    clearMarketData,
    refreshFromCache,
    isConnected,
    error,
    stats,
    lastUpdate,
    totalInstruments: marketData.size,
    isLoading,
    cacheLoaded,
  }
}

// Hook for connection status monitoring
export function useWebSocketStatus() {
  const [stats, setStats] = useState(getWebSocketStats())

  useEffect(() => {
    const interval = setInterval(() => {
      setStats(getWebSocketStats())
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  return stats
}
