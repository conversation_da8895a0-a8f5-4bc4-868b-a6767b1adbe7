/**
 * Fallback In-Memory Cache for Development
 * Used when Redis is not available or in development mode
 */

import { MarketData } from '@/types';

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

class FallbackCache {
  private cache = new Map<string, CacheEntry>();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startCleanup();
  }

  private startCleanup() {
    // Clean expired entries every 30 seconds
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 30000);
  }

  private cleanup() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl * 1000) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 FallbackCache: Cleaned ${cleaned} expired entries`);
    }
  }

  set(key: string, data: any, ttlSeconds = 600): boolean {
    try {
      this.cache.set(key, {
        data,
        timestamp: Date.now(),
        ttl: ttlSeconds
      });
      console.log(`💾 FallbackCache: Cached ${key} (TTL: ${ttlSeconds}s)`);
      return true;
    } catch (error) {
      console.error(`❌ FallbackCache: Failed to cache ${key}:`, error);
      return false;
    }
  }

  get<T = any>(key: string): T | null {
    try {
      const entry = this.cache.get(key);
      if (!entry) {
        return null;
      }

      const now = Date.now();
      if (now > entry.timestamp + entry.ttl * 1000) {
        this.cache.delete(key);
        return null;
      }

      console.log(`📖 FallbackCache: Retrieved ${key} from cache`);
      return entry.data as T;
    } catch (error) {
      console.error(`❌ FallbackCache: Failed to retrieve ${key}:`, error);
      return null;
    }
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      console.log(`🗑️ FallbackCache: Removed ${key}`);
    }
    return deleted;
  }

  clear(pattern?: string): number {
    if (!pattern) {
      const size = this.cache.size;
      this.cache.clear();
      console.log(`🧹 FallbackCache: Cleared all ${size} entries`);
      return size;
    }

    let cleared = 0;
    const regex = new RegExp(pattern.replace('*', '.*'));
    
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
        cleared++;
      }
    }

    console.log(`🧹 FallbackCache: Cleared ${cleared} entries matching ${pattern}`);
    return cleared;
  }

  getStats() {
    const totalKeys = this.cache.size;
    const memoryUsage = `${Math.round(JSON.stringify(Array.from(this.cache.entries())).length / 1024)}KB`;
    
    return {
      totalKeys,
      memoryUsage,
      connectedClients: 1,
      uptime: process.uptime()
    };
  }

  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cache.clear();
    console.log('👋 FallbackCache: Destroyed');
  }
}

// Global instance
let fallbackCache: FallbackCache | null = null;

export function getFallbackCache(): FallbackCache {
  if (!fallbackCache) {
    fallbackCache = new FallbackCache();
    console.log('🚀 FallbackCache: Initialized in-memory cache for development');
  }
  return fallbackCache;
}

export default FallbackCache;
