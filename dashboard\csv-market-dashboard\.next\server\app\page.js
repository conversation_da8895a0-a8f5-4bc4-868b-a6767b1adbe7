(()=>{var e={};e.id=931,e.ids=[931],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9485:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>o}),s(5480),s(2029),s(5866);var a=s(3191),r=s(8716),n=s(7922),l=s.n(n),i=s(5231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5480)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\love\\dashboard\\csv-market-dashboard\\src\\app\\page.tsx"],h="/page",m={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5961:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7134:(e,t,s)=>{Promise.resolve().then(s.bind(s,7480))},4193:(e,t,s)=>{Promise.resolve().then(s.bind(s,1165))},7480:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(326),r=s(6465),n=s.n(r),l=s(381),i=s(938);function c({error:e,resetErrorBoundary:t}){return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg border border-red-200",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3",children:a.jsx("span",{className:"text-red-600 text-lg",children:"⚠"})}),a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Application Error"})]}),a.jsx("p",{className:"text-gray-600 mb-4",children:"An unexpected error occurred. Please try refreshing the page or contact support if the problem persists."}),(0,a.jsxs)("details",{className:"mb-4",children:[a.jsx("summary",{className:"cursor-pointer text-sm text-gray-500 hover:text-gray-700",children:"Technical Details"}),a.jsx("pre",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto max-h-32",children:e.message})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:t,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Try Again"}),a.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors",children:"Refresh Page"})]})]})})}function o({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("title",{children:"CSV Market Dashboard"}),a.jsx("meta",{name:"description",content:"Real-time market data dashboard with enhanced WebSocket management and data caching"}),a.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),a.jsx("link",{rel:"icon",href:"/favicon.ico"})]}),a.jsx("body",{className:n().className,children:a.jsx(i.SV,{FallbackComponent:c,onError:(e,t)=>{console.error("Application Error:",e,t)},onReset:()=>{localStorage.removeItem("enhanced-market-data"),localStorage.removeItem("enhanced-market-timestamp"),sessionStorage.clear()},children:(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 via-blue-50/30 to-indigo-50/20",children:[e,a.jsx(l.x7,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{style:{background:"#10B981"}},error:{style:{background:"#EF4444"}}}})]})})})]})}s(3824)},1165:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var a=s(326),r=s(7577);let n=({instruments:e,marketData:t,onInstrumentSelect:s,loading:n=!1})=>{let[l,i]=(0,r.useState)("symbol"),[c,o]=(0,r.useState)("asc"),d=(0,r.useMemo)(()=>[...e].sort((e,t)=>{let s=e[l],a=t[l];if(void 0===s&&void 0===a)return 0;if(void 0===s)return 1;if(void 0===a)return -1;if(s===a)return 0;let r=s<a?-1:1;return"asc"===c?r:-r}),[e,l,c]),h=e=>{l===e?o("asc"===c?"desc":"asc"):(i(e),o("asc"))},m=e=>void 0===e||0===e?"-":`₹${e.toFixed(2)}`,u=(e,t)=>{if(void 0===e||void 0===t)return"-";let s=e>=0?"+":"";return`${s}${e.toFixed(2)} (${s}${t.toFixed(2)}%)`},x=e=>void 0===e||0===e?"text-gray-600":e>0?"text-green-600":"text-red-600",g=e=>void 0===e||0===e?"-":e>=1e7?`${(e/1e7).toFixed(1)}Cr`:e>=1e5?`${(e/1e5).toFixed(1)}L`:e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),p=({field:e})=>l!==e?a.jsx("span",{className:"text-gray-400",children:"↕"}):"asc"===c?a.jsx("span",{className:"text-blue-600",children:"↑"}):a.jsx("span",{className:"text-blue-600",children:"↓"});return n?a.jsx("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[a.jsx("div",{className:"spinner w-8 h-8 mr-3"}),a.jsx("span",{className:"text-gray-600",children:"Loading instruments..."})]})}):0===e.length?a.jsx("div",{className:"glass rounded-2xl shadow-lg p-6",children:(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx("p",{className:"text-gray-600 text-lg",children:"No instruments found"}),a.jsx("p",{className:"text-gray-500 text-sm mt-2",children:"Try adjusting your filters"})]})}):(0,a.jsxs)("div",{className:"glass rounded-2xl shadow-lg overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[a.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Market Instruments"}),(0,a.jsxs)("p",{className:"text-gray-600 text-sm mt-1",children:["Showing ",d.length," instruments"]})]}),a.jsx("div",{className:"overflow-x-auto custom-scrollbar",style:{maxHeight:"600px"},children:(0,a.jsxs)("table",{className:"market-table",children:[a.jsx("thead",{className:"sticky top-0 bg-gray-50 z-10",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("securityId"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Security ID",a.jsx(p,{field:"securityId"})]})}),a.jsx("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("symbol"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Symbol",a.jsx(p,{field:"symbol"})]})}),a.jsx("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("displayName"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Name",a.jsx(p,{field:"displayName"})]})}),a.jsx("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("exchange"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Exchange",a.jsx(p,{field:"exchange"})]})}),a.jsx("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("instrumentType"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Type",a.jsx(p,{field:"instrumentType"})]})}),a.jsx("th",{className:"text-right",children:"LTP"}),a.jsx("th",{className:"text-right",children:"Change"}),a.jsx("th",{className:"text-right",children:"Volume"}),a.jsx("th",{className:"cursor-pointer hover:bg-gray-100",onClick:()=>h("lotSize"),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:["Lot Size",a.jsx(p,{field:"lotSize"})]})})]})}),a.jsx("tbody",{children:d.map(e=>{let r=t.get(e.securityId);return(0,a.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>s?.(e),children:[a.jsx("td",{className:"font-mono text-sm text-gray-700",children:e.securityId}),a.jsx("td",{className:"font-medium text-blue-600",children:e.symbol}),a.jsx("td",{className:"max-w-xs truncate",title:e.displayName,children:e.displayName}),a.jsx("td",{children:a.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.exchange})}),a.jsx("td",{children:a.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e.instrumentType})}),a.jsx("td",{className:"text-right font-medium",children:m(r?.ltp)}),a.jsx("td",{className:`text-right font-medium ${x(r?.change)}`,children:u(r?.change,r?.changePercent)}),a.jsx("td",{className:"text-right",children:g(r?.volume)}),a.jsx("td",{className:"text-right",children:e.lotSize.toLocaleString()})]},e.securityId)})})]})}),d.length>100&&a.jsx("div",{className:"p-4 bg-gray-50 border-t border-gray-200",children:a.jsx("p",{className:"text-sm text-gray-600 text-center",children:"Showing first 100 instruments. Use filters to narrow down results."})})]})},l=({filter:e,onFilterChange:t,exchanges:s,instrumentTypes:n,segments:l})=>{let[i,c]=(0,r.useState)(e.search||""),o=(s,a)=>{let r=e.exchange||[],n=a?[...r,s]:r.filter(e=>e!==s);t({...e,exchange:n.length>0?n:void 0})},d=(s,a)=>{let r=e.instrumentType||[],n=a?[...r,s]:r.filter(e=>e!==s);t({...e,instrumentType:n.length>0?n:void 0})},h=(s,a)=>{let r=e.segment||[],n=a?[...r,s]:r.filter(e=>e!==s);t({...e,segment:n.length>0?n:void 0})},m=s=>{c(s),t({...e,search:s||void 0})},u=()=>!!(e.exchange?.length||e.instrumentType?.length||e.segment?.length||e.search||void 0!==e.isActive||void 0!==e.hasExpiry);return(0,a.jsxs)("div",{className:"filter-panel",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Filters"}),u()&&a.jsx("button",{onClick:()=>{c(""),t({})},className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Clear All"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[a.jsx("label",{className:"filter-label",children:"Search"}),a.jsx("input",{type:"text",placeholder:"Search by symbol, name, or ISIN...",value:i,onChange:e=>m(e.target.value),className:"filter-input"})]}),(0,a.jsxs)("div",{className:"filter-group",children:[a.jsx("label",{className:"filter-label",children:"Exchanges"}),a.jsx("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:s.map(t=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e.exchange?.includes(t)||!1,onChange:e=>o(t,e.target.checked),className:"filter-checkbox"}),a.jsx("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),(0,a.jsxs)("div",{className:"filter-group",children:[a.jsx("label",{className:"filter-label",children:"Instrument Types"}),a.jsx("div",{className:"space-y-2 max-h-32 overflow-y-auto custom-scrollbar",children:n.map(t=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e.instrumentType?.includes(t)||!1,onChange:e=>d(t,e.target.checked),className:"filter-checkbox"}),a.jsx("span",{className:"text-sm text-gray-700",children:t})]},t))})]}),(0,a.jsxs)("div",{className:"filter-group",children:[a.jsx("label",{className:"filter-label",children:"Segments"}),a.jsx("div",{className:"space-y-2",children:l.map(t=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:e.segment?.includes(t)||!1,onChange:e=>h(t,e.target.checked),className:"filter-checkbox"}),a.jsx("span",{className:"text-sm text-gray-700",children:"C"===t?"Cash (C)":"F"===t?"Futures (F)":"O"===t?"Options (O)":t})]},t))})]}),(0,a.jsxs)("div",{className:"filter-group",children:[a.jsx("label",{className:"filter-label",children:"Additional Filters"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:!0===e.isActive,onChange:s=>t({...e,isActive:!!s.target.checked||void 0}),className:"filter-checkbox"}),a.jsx("span",{className:"text-sm text-gray-700",children:"Active Only"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:!0===e.hasExpiry,onChange:s=>t({...e,hasExpiry:!!s.target.checked||void 0}),className:"filter-checkbox"}),a.jsx("span",{className:"text-sm text-gray-700",children:"Has Expiry"})]})]})]}),(0,a.jsxs)("div",{className:"filter-group",children:[a.jsx("label",{className:"filter-label",children:"Lot Size Range"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[a.jsx("input",{type:"number",placeholder:"Min",value:e.minLotSize||"",onChange:s=>t({...e,minLotSize:s.target.value?parseInt(s.target.value):void 0}),className:"filter-input text-sm"}),a.jsx("input",{type:"number",placeholder:"Max",value:e.maxLotSize||"",onChange:s=>t({...e,maxLotSize:s.target.value?parseInt(s.target.value):void 0}),className:"filter-input text-sm"})]})]}),u()&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[a.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Active Filters:"}),(0,a.jsxs)("div",{className:"space-y-1 text-xs text-blue-700",children:[e.exchange?.length&&(0,a.jsxs)("div",{children:["Exchanges: ",e.exchange.join(", ")]}),e.instrumentType?.length&&(0,a.jsxs)("div",{children:["Types: ",e.instrumentType.join(", ")]}),e.segment?.length&&(0,a.jsxs)("div",{children:["Segments: ",e.segment.join(", ")]}),e.search&&(0,a.jsxs)("div",{children:["Search: “",e.search,"”"]}),e.isActive&&a.jsx("div",{children:"Active instruments only"}),e.hasExpiry&&a.jsx("div",{children:"With expiry date"}),(e.minLotSize||e.maxLotSize)&&(0,a.jsxs)("div",{children:["Lot size: ",e.minLotSize||0," - ",e.maxLotSize||"∞"]})]})]})]})},i=({connected:e})=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:`w-3 h-3 rounded-full ${e?"bg-green-500 animate-pulse":"bg-red-500"}`}),a.jsx("span",{className:`text-sm font-medium ${e?"text-green-700":"text-red-700"}`,children:e?"Connected":"Disconnected"})]}),a.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${e?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e?"LIVE":"OFFLINE"})]}),c=({totalInstruments:e,filteredInstruments:t,marketDataCount:s,connected:r,connectionStats:n})=>{let l;let i=[{label:"Total Instruments",value:e.toLocaleString(),icon:"\uD83D\uDCCA",color:"bg-blue-500",description:"Available instruments"},{label:"Filtered Results",value:t.toLocaleString(),icon:"\uD83D\uDD0D",color:"bg-purple-500",description:"Matching filters"},{label:"Live Data",value:s.toLocaleString(),icon:"\uD83D\uDCC8",color:r?"bg-green-500":"bg-gray-500",description:n?`${n.connectedInstruments} active`:"Market data points"},{label:"Connection",value:r?"Active":"Inactive",icon:r?"\uD83D\uDFE2":"\uD83D\uDD34",color:r?"bg-green-500":"bg-red-500",description:n?`${n.messagesReceived} messages`:"WebSocket status"}],c=n?[{label:"Cache Size",value:n.cacheSize.toLocaleString(),icon:"\uD83D\uDCBE",color:"bg-indigo-500",description:n.isAutoSaving?"Auto-saving":"Manual save"},{label:"Last Update",value:(e=>{if(!e)return"Never";let t=Math.floor((new Date().getTime()-e.getTime())/1e3);return t<60?`${t}s ago`:t<3600?`${Math.floor(t/60)}m ago`:e.toLocaleTimeString()})(n.lastUpdate),icon:"\uD83D\uDD52",color:"bg-orange-500",description:"Data freshness"},{label:"Uptime",value:(l=n.connectionUptime)<60?`${l}s`:l<3600?`${Math.floor(l/60)}m ${l%60}s`:`${Math.floor(l/3600)}h ${Math.floor(l%3600/60)}m`,icon:"⏱️",color:"bg-teal-500",description:"Connection stability"},{label:"Reconnects",value:n.reconnectAttempts.toString(),icon:n.reconnectAttempts>0?"\uD83D\uDD04":"✅",color:n.reconnectAttempts>0?"bg-yellow-500":"bg-green-500",description:"Connection reliability"}]:[];return(0,a.jsxs)("div",{className:"space-y-6 mb-6",children:[a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:i.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.label}),a.jsx("p",{className:"text-2xl font-bold text-gray-900 mt-1",children:e.value}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),a.jsx("div",{className:"text-2xl ml-3",children:e.icon})]}),a.jsx("div",{className:`mt-3 h-1 rounded-full ${e.color}`})]},t))}),c.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[a.jsx("span",{className:"text-xl mr-2",children:"⚡"}),"Real-time Connection Stats"]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:c.map((e,t)=>(0,a.jsxs)("div",{className:"glass rounded-xl shadow-lg p-4 card-hover border-l-4 border-l-blue-400",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600",children:e.label}),a.jsx("p",{className:"text-xl font-bold text-gray-900 mt-1",children:e.value}),a.jsx("p",{className:"text-xs text-gray-500 mt-1",children:e.description})]}),a.jsx("div",{className:"text-xl ml-3",children:e.icon})]}),a.jsx("div",{className:`mt-3 h-1 rounded-full ${e.color}`})]},t))})]})]})},o=(e={})=>{let{autoConnect:t=!0,autoLoadCache:s=!0,autoSaveInterval:a=3e4,reconnectOnError:n=!0,maxReconnectAttempts:l=5}=e,i={status:"disconnected",isConnected:!1,error:null,connectionStats:{totalMessages:0,connectionUptime:0}},c=new Map,o={isLoaded:!1,totalCacheSize:0,pendingUpdates:0,lastCacheUpdate:null},d={filters:{},sortConfig:{field:"symbol",direction:"asc"},selectedInstruments:new Set,viewMode:"table",autoRefresh:!0},h=(0,r.useCallback)(async()=>{},[]),m=(0,r.useCallback)(async()=>{},[]),u=(0,r.useCallback)(()=>{},[]),x=(0,r.useCallback)(async()=>{},[]),g=(0,r.useCallback)(async()=>{},[]),p=(0,r.useCallback)(async e=>{},[]),b=(0,r.useCallback)(async()=>{},[]),f=(0,r.useCallback)(e=>{},[]),v=(0,r.useCallback)((e,t)=>{},[]),y=(0,r.useCallback)(()=>[],[]),j=(0,r.useCallback)(()=>[],[]),N=(0,r.useCallback)(e=>void 0,[]),S=(0,r.useCallback)(e=>void 0,[]),C=(0,r.useCallback)(e=>{},[]),D=(0,r.useCallback)(e=>{},[]),E=(0,r.useCallback)(()=>{},[]),k=(0,r.useRef)(null),T=(0,r.useRef)(0),w=(0,r.useRef)(!1),_=Array.from(c.values());(0,r.useEffect)(()=>((async()=>{if(!w.current){w.current=!0,console.log("\uD83D\uDE80 Enhanced Hook: Initializing...");try{s&&!o.isLoaded&&await g(),t&&"disconnected"===i.status&&await h()}catch(e){console.error("❌ Enhanced Hook: Initialization failed:",e)}}})(),()=>{k.current&&clearInterval(k.current)}),[t,s,o.isLoaded,i.status,h,g]),(0,r.useEffect)(()=>{if(a>0&&_.length>0)return k.current&&clearInterval(k.current),k.current=setInterval(()=>{p()},a),()=>{k.current&&clearInterval(k.current)}},[a,_.length,p]),(0,r.useEffect)(()=>{if(n&&"error"===i.status&&T.current<l){let e=Math.min(1e3*Math.pow(2,T.current),3e4);console.log(`🔄 Enhanced Hook: Auto-reconnecting in ${e}ms (attempt ${T.current+1}/${l})`);let t=setTimeout(async()=>{try{T.current++,await x()}catch(e){console.error("❌ Enhanced Hook: Auto-reconnect failed:",e)}},e);return()=>clearTimeout(t)}},[i.status,x,n,l]),(0,r.useEffect)(()=>{"connected"===i.status&&(T.current=0)},[i.status]);let I=(0,r.useCallback)(()=>y(),[y]),A=(0,r.useCallback)(()=>j(),[j]),L=(0,r.useCallback)(e=>N(e),[N]),R=(0,r.useCallback)(e=>S(e),[S]),M=(0,r.useCallback)(e=>{f(e)},[f]),P=(0,r.useCallback)((e,t)=>{v(e,t)},[v]),$=(0,r.useCallback)(e=>{C(e)},[C]),F=(0,r.useCallback)(e=>{D(e)},[D]),U=(0,r.useCallback)(async()=>{try{T.current=0,await x()}catch(e){throw console.error("❌ Enhanced Hook: Manual reconnect failed:",e),e}},[x]),z=(0,r.useCallback)(async()=>{try{await g()}catch(e){throw console.error("❌ Enhanced Hook: Force refresh failed:",e),e}},[g]),O=(0,r.useCallback)(async()=>{try{await p(!0)}catch(e){throw console.error("❌ Enhanced Hook: Force save failed:",e),e}},[p]),H={totalInstruments:_.length,connectedInstruments:_.filter(e=>e.ltp&&e.ltp>0).length,lastUpdate:null,cacheSize:o.totalCacheSize,connectionUptime:i.connectionStats.connectionUptime,messagesReceived:i.connectionStats.totalMessages,reconnectAttempts:T.current,isAutoSaving:null!==k.current};return{marketData:_,marketDataMap:c,filteredData:I(),sortedData:A(),isConnected:i.isConnected,connectionStatus:i.status,connectionError:i.error,connectionStats:i.connectionStats,cacheLoaded:o.isLoaded,cacheUpdating:o.pendingUpdates>0,lastCacheUpdate:o.lastCacheUpdate,filters:d.filters,sortConfig:d.sortConfig,selectedInstruments:d.selectedInstruments,viewMode:d.viewMode,autoRefresh:d.autoRefresh,isLoading:!1,isInitializing:!w.current,getDataBySecurityId:L,getDataBySymbol:R,getFilteredData:I,getSortedData:A,updateFilters:M,updateSort:P,subscribe:$,unsubscribe:F,connect:m,disconnect:u,reconnect:U,refresh:z,save:O,clearCache:b,reset:E,stats:H,_store:{setFilters:f,setSortConfig:v,subscribeToInstrument:C,unsubscribeFromInstrument:D}}};var d=s(5767);function h(){let{marketData:e,marketDataMap:t,isConnected:s,connectionError:h,connectionStatus:m,isLoading:u,cacheLoaded:x,refresh:g,stats:p,updateFilters:b,filters:f,getFilteredData:v,getSortedData:y}=o(),[j,N]=(0,r.useState)([]),[S,C]=(0,r.useState)(new Map),[D,E]=(0,r.useState)({}),[k,T]=(0,r.useState)(!0),[w,_]=(0,r.useState)(null),[I,A]=(0,r.useState)([]),[L,R]=(0,r.useState)([]);(0,r.useCallback)(async()=>{try{T(!0);let e=await d.qn.getCachedStaticData(d.A_.INSTRUMENTS);if(e&&Array.isArray(e)){console.log("✅ Dashboard: Loaded instruments from cache"),N(e),T(!1),M();return}await M()}catch(e){console.error("❌ Dashboard: Error loading instruments:",e),_("Error loading instruments"),T(!1)}},[j.length]);let M=async()=>{try{console.log("\uD83C\uDF10 Dashboard: Loading fresh instruments from API...");let e=await fetch("http://localhost:8080]/api/instruments");if(e.ok){let t=await e.json();console.log("✅ Dashboard: Loaded",t.data.instruments.length,"instruments from API"),N(t.data.instruments),await d.qn.cacheStaticData(d.A_.INSTRUMENTS,t.data.instruments),console.log("\uD83D\uDCBE Dashboard: Cached instruments data"),T(!1)}else console.error("❌ Dashboard: Failed to load instruments:",e.statusText),_("Failed to load instruments"),T(!1)}catch(e){console.error("❌ Dashboard: Error fetching fresh instruments:",e),0===j.length&&(_("Error loading instruments"),T(!1))}},P=j.filter(e=>{if(D.exchange&&D.exchange.length>0&&!D.exchange.includes(e.exchange)||D.instrumentType&&D.instrumentType.length>0&&!D.instrumentType.includes(e.instrumentType))return!1;if(D.search){let t=D.search.toLowerCase();return e.symbol.toLowerCase().includes(t)||e.displayName.toLowerCase().includes(t)||e.isin&&e.isin.toLowerCase().includes(t)}return!0});return k?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"spinner w-12 h-12 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading market data..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen p-6",children:[a.jsx("div",{className:"glass rounded-2xl shadow-lg p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold gradient-text",children:"CSV Market Dashboard"}),a.jsx("p",{className:"text-gray-600 mt-1",children:"Real-time market data from CSV instruments"}),(0,a.jsxs)("div",{className:"mt-3 space-x-3",children:[a.jsx("a",{href:"/subscribed",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors",children:"\uD83D\uDCCA View Subscribed Data Dashboard"}),a.jsx("a",{href:"/option-chain",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors",children:"\uD83D\uDD17 NIFTY Option Chain"})]})]}),a.jsx(i,{connected:s})]})}),(w||h)&&(0,a.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:[a.jsx("strong",{children:"Error:"})," ",w||h]}),a.jsx(c,{totalInstruments:j.length,filteredInstruments:P.length,marketDataCount:p.totalInstruments,connected:s,connectionStats:p}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[a.jsx("div",{className:"lg:col-span-1",children:a.jsx(l,{filter:D,onFilterChange:e=>{E(e)},exchanges:I,instrumentTypes:L,segments:["C","F","O"]})}),a.jsx("div",{className:"lg:col-span-3",children:a.jsx(n,{instruments:P.slice(0,100),marketData:S,onInstrumentSelect:e=>{console.log("Selected instrument:",e)},loading:k})})]}),(0,a.jsxs)("div",{className:"mt-8 text-center text-sm text-gray-500",children:[(0,a.jsxs)("p",{children:["CSV Market Dashboard - Real-time data from ",j.length," instruments"]}),(0,a.jsxs)("p",{children:["Last updated: ",new Date().toLocaleTimeString()]})]})]})}},3566:(e,t,s)=>{"use strict";s.d(t,{Hh:()=>r,Hz:()=>a,ej:()=>n});let a={BASE_URL:process.env.NEXT_PUBLIC_API_URL||"http://localhost:8080",TIMEOUT:3e4,RETRY_ATTEMPTS:3,RETRY_DELAY:1e3},r={RECONNECT_INTERVAL:3e3,MAX_RECONNECT_ATTEMPTS:15,PING_INTERVAL:25e3,PONG_TIMEOUT:1e4,CONNECTION_TIMEOUT:2e4,HEARTBEAT_INTERVAL:3e4,MAX_LISTENERS_PER_EVENT:10,CLEANUP_INTERVAL:6e4},n={DEFAULT_TTL:3e5,MARKET_DATA_TTL:6e5,STATIC_DATA_TTL:18e5,EXPIRY_CHECK_INTERVAL:6e4,MAX_CACHE_SIZE:52428800,KEYS:{INSTRUMENTS:"instruments",MARKET_DATA:"market_data",OPTION_CHAIN:"option_chain",EXPIRY_DATES:"expiry_dates",NIFTY_SPOT:"nifty_spot",USER_SETTINGS:"user_settings",USER_PREFERENCES:"user_preferences"}};parseInt(process.env.PORT||"8080"),parseInt(process.env.NEXT_PORT||"3000"),process.env.LOG_LEVEL,process.env.ENABLE_METRICS},5767:(e,t,s)=>{"use strict";s.d(t,{A_:()=>l,qn:()=>i});var a=s(3566);class r{static{this.instance=null}isBrowser(){return!1}static getInstance(){return r.instance||(r.instance=new r),r.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch{this.compressionSupported=!1}}async set(e,t,s={}){try{if(!this.isBrowser())return console.log(`⚠️ DataCache: Skipping cache on server side for ${e}`),!1;let{ttl:r=a.ej.DEFAULT_TTL,useCompression:n=!1,storage:l="localStorage"}=s,i={data:t,timestamp:Date.now(),ttl:r,version:this.version},c=JSON.stringify(i);n&&this.compressionSupported&&(c=await this.compress(c));let o="localStorage"===l?localStorage:sessionStorage,d=this.getFullKey(e);return o.setItem(d,c),console.log(`💾 DataCache: Cached ${e} (${c.length} bytes)`),!0}catch(t){return console.error(`❌ DataCache: Failed to cache ${e}:`,t),!1}}async get(e,t={}){try{if(!this.isBrowser())return null;let{storage:s="localStorage"}=t,a="localStorage"===s?localStorage:sessionStorage,r=this.getFullKey(e),n=a.getItem(r);if(!n)return null;let l=n;this.compressionSupported&&this.isCompressed(n)&&(l=await this.decompress(n));let i=JSON.parse(l);if(i.version!==this.version)return console.warn(`⚠️ DataCache: Version mismatch for ${e}, removing`),this.remove(e,t),null;if(Date.now()-i.timestamp>i.ttl)return console.log(`⏰ DataCache: ${e} expired, removing`),this.remove(e,t),null;return console.log(`📖 DataCache: Retrieved ${e} from cache`),i.data}catch(s){return console.error(`❌ DataCache: Failed to retrieve ${e}:`,s),this.remove(e,t),null}}remove(e,t={}){try{if(!this.isBrowser())return;let{storage:s="localStorage"}=t,a="localStorage"===s?localStorage:sessionStorage,r=this.getFullKey(e);a.removeItem(r),console.log(`🗑️ DataCache: Removed ${e}`)}catch(t){console.error(`❌ DataCache: Failed to remove ${e}:`,t)}}clear(e="localStorage"){try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,s=[];for(let e=0;e<t.length;e++){let a=t.key(e);a&&a.startsWith("csv_market_dashboard_cache_")&&s.push(a)}s.forEach(e=>t.removeItem(e)),console.log(`🧹 DataCache: Cleared ${s.length} entries from ${e}`)}catch(t){console.error(`❌ DataCache: Failed to clear ${e}:`,t)}}getStats(e="localStorage"){if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,s=0,a=0,r=1/0,n=0;try{for(let e=0;e<t.length;e++){let l=t.key(e);if(l&&l.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(l);if(e){s++,a+=e.length;try{let t=JSON.parse(e);t.timestamp&&(r=Math.min(r,t.timestamp),n=Math.max(n,t.timestamp))}catch{}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:s,totalSize:a,oldestEntry:r===1/0?null:new Date(r),newestEntry:0===n?null:new Date(n)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},a.ej.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,s=[];for(let e=0;e<t.length;e++){let a=t.key(e);if(a&&a.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(a);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&s.push(a)}}catch{s.push(a)}}s.forEach(e=>t.removeItem(e)),s.length>0&&console.log(`🧹 DataCache: Cleaned up ${s.length} expired entries from ${e}`)})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return`csv_market_dashboard_cache_${e}`}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}}let n=r.getInstance(),l=a.ej.KEYS,i={cacheMarketData:async(e,t)=>n.set(e,t,{ttl:a.ej.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>n.set(e,t,{ttl:a.ej.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>n.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>n.get(e,{storage:"localStorage"})}},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(8570).createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\layout.tsx#default`)},5480:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(8570).createProxy)(String.raw`D:\love\dashboard\csv-market-dashboard\src\app\page.tsx#default`)},3824:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[276,314],()=>s(9485));module.exports=a})();