/**
 * Optimized Socket.IO Configuration for Stable Connections
 */

import { Server as SocketIOServer, ServerOptions } from 'socket.io'
import { Server as HTTPServer } from 'http'
import { WEBSOCKET_CONFIG, SECURITY_CONFIG } from '../lib/constants'

/**
 * Create optimized Socket.IO configuration
 */
export function createSocketConfig(): Partial<ServerOptions> {
  return {
    cors: {
      origin: SECURITY_CONFIG.CORS.ORIGIN,
      credentials: SECURITY_CONFIG.CORS.CREDENTIALS,
    },
    transports: ['websocket', 'polling'] as any,
    pingTimeout: WEBSOCKET_CONFIG.PONG_TIMEOUT,
    pingInterval: WEBSOCKET_CONFIG.PING_INTERVAL,
    upgradeTimeout: 10000,
    maxHttpBufferSize: 1e6, // 1MB
    allowEIO3: true,
    connectTimeout: WEBSOCKET_CONFIG.CONNECTION_TIMEOUT,
    serveClient: false,
  }
}

/**
 * Setup Socket.IO server with optimized configuration
 */
export function setupSocketServer(httpServer: HTTPServer): SocketIOServer {
  const config = createSocketConfig()
  
  const io = new SocketIOServer(httpServer, config)

  // Connection tracking
  const connectedClients = new Set<string>()
  const clientMetrics = new Map<string, {
    connectedAt: number
    lastActivity: number
    messagesSent: number
    messagesReceived: number
  }>()

  // Middleware for connection logging
  io.use((socket, next) => {
    const clientIP = socket.handshake.address
    const userAgent = socket.handshake.headers['user-agent']
    
    console.log(`🔌 New connection attempt from ${clientIP}`)
    console.log(`📱 User Agent: ${userAgent}`)
    
    next()
  })

  // Connection handler
  io.on('connection', (socket) => {
    const clientId = socket.id
    const connectedAt = Date.now()
    
    // Add to tracking
    connectedClients.add(clientId)
    clientMetrics.set(clientId, {
      connectedAt,
      lastActivity: connectedAt,
      messagesSent: 0,
      messagesReceived: 0,
    })

    console.log(`✅ Client connected: ${clientId} (Total: ${connectedClients.size})`)

    // Send initial connection data
    socket.emit('initialData', {
      connectionId: clientId,
      serverTime: new Date().toISOString(),
      connected: true,
      totalClients: connectedClients.size,
    })

    // Heartbeat mechanism
    const heartbeatInterval = setInterval(() => {
      if (socket.connected) {
        socket.emit('heartbeat', { timestamp: Date.now() })
      } else {
        clearInterval(heartbeatInterval)
      }
    }, WEBSOCKET_CONFIG.HEARTBEAT_INTERVAL)

    // Handle ping/pong
    socket.on('ping', (data) => {
      const metrics = clientMetrics.get(clientId)
      if (metrics) {
        metrics.lastActivity = Date.now()
        metrics.messagesReceived++
      }
      socket.emit('pong', { 
        timestamp: Date.now(),
        clientTimestamp: data?.timestamp 
      })
    })

    socket.on('pong', (data) => {
      const metrics = clientMetrics.get(clientId)
      if (metrics) {
        metrics.lastActivity = Date.now()
        metrics.messagesReceived++
      }
    })

    // Handle market data subscription
    socket.on('subscribe', (data: { securityIds: string[] }) => {
      console.log(`📊 Subscription request from ${clientId}:`, data.securityIds?.length || 0, 'instruments')
      
      // For now, subscriptions are handled server-side
      socket.emit('subscription_disabled', {
        message: 'Client subscriptions disabled. Using server auto-subscription for NSE derivatives only.',
        serverManaged: true,
      })
    })

    // Handle unsubscription
    socket.on('unsubscribe', (data: { securityIds: string[] }) => {
      console.log(`📊 Unsubscription request from ${clientId}:`, data.securityIds?.length || 0, 'instruments')
      
      socket.emit('unsubscription_disabled', {
        message: 'Client unsubscriptions disabled. Using server auto-subscription for NSE derivatives only.',
        serverManaged: true,
      })
    })

    // Handle client errors
    socket.on('error', (error) => {
      console.error(`❌ Socket error for ${clientId}:`, error.message)
    })

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      clearInterval(heartbeatInterval)
      connectedClients.delete(clientId)
      
      const metrics = clientMetrics.get(clientId)
      if (metrics) {
        const sessionDuration = Date.now() - metrics.connectedAt
        console.log(`❌ Client disconnected: ${clientId}`)
        console.log(`   Reason: ${reason}`)
        console.log(`   Session duration: ${Math.round(sessionDuration / 1000)}s`)
        console.log(`   Messages sent: ${metrics.messagesSent}`)
        console.log(`   Messages received: ${metrics.messagesReceived}`)
        console.log(`   Total clients: ${connectedClients.size}`)
        
        clientMetrics.delete(clientId)
      }

      // Log specific disconnect reasons for debugging
      switch (reason) {
        case 'transport close':
          console.warn(`⚠️ Network transport closed for ${clientId}`)
          break
        case 'transport error':
          console.warn(`⚠️ Network transport error for ${clientId}`)
          break
        case 'client namespace disconnect':
          console.log(`👋 Client initiated disconnect for ${clientId}`)
          break
        case 'server namespace disconnect':
          console.log(`🔌 Server initiated disconnect for ${clientId}`)
          break
        case 'ping timeout':
          console.warn(`⏰ Ping timeout for ${clientId}`)
          break
        default:
          console.log(`❓ Unknown disconnect reason for ${clientId}: ${reason}`)
      }
    })

    // Handle disconnecting event
    socket.on('disconnecting', (reason) => {
      console.log(`⚠️ Client disconnecting: ${clientId} (Reason: ${reason})`)
    })

    // Update activity on any message
    socket.onAny((event, ...args) => {
      const metrics = clientMetrics.get(clientId)
      if (metrics) {
        metrics.lastActivity = Date.now()
        if (event !== 'ping' && event !== 'pong') {
          metrics.messagesReceived++
        }
      }
    })

    // Track outgoing messages
    const originalEmit = socket.emit
    socket.emit = function(event: string, ...args: any[]) {
      const metrics = clientMetrics.get(clientId)
      if (metrics && event !== 'ping' && event !== 'pong') {
        metrics.messagesSent++
      }
      return originalEmit.call(this, event, ...args)
    }
  })

  // Server-level error handling
  io.engine.on('connection_error', (err) => {
    console.error('🔥 Socket.IO connection error:', {
      message: err.message,
      description: err.description,
      context: err.context,
      type: err.type,
    })
  })

  // Periodic cleanup of stale connections
  setInterval(() => {
    const now = Date.now()
    const staleThreshold = 5 * 60 * 1000 // 5 minutes

    for (const [clientId, metrics] of clientMetrics.entries()) {
      if (now - metrics.lastActivity > staleThreshold) {
        console.warn(`🧹 Cleaning up stale connection: ${clientId}`)
        const socket = io.sockets.sockets.get(clientId)
        if (socket) {
          socket.disconnect(true)
        }
        clientMetrics.delete(clientId)
        connectedClients.delete(clientId)
      }
    }
  }, WEBSOCKET_CONFIG.CLEANUP_INTERVAL)

  // Periodic stats logging
  setInterval(() => {
    console.log(`📊 Connection Stats:`)
    console.log(`   Active connections: ${connectedClients.size}`)
    console.log(`   Total messages sent: ${Array.from(clientMetrics.values()).reduce((sum, m) => sum + m.messagesSent, 0)}`)
    console.log(`   Total messages received: ${Array.from(clientMetrics.values()).reduce((sum, m) => sum + m.messagesReceived, 0)}`)
  }, 60000) // Every minute

  return io
}

/**
 * Broadcast market data to all connected clients
 */
export function broadcastMarketData(io: SocketIOServer, data: any): void {
  if (io.sockets.sockets.size > 0) {
    io.emit('marketData', data)
  }
}

/**
 * Broadcast market data batch to all connected clients
 */
export function broadcastMarketDataBatch(io: SocketIOServer, dataArray: any[]): void {
  if (io.sockets.sockets.size > 0 && dataArray.length > 0) {
    io.emit('marketDataBatch', dataArray)
  }
}

/**
 * Get connection statistics
 */
export function getConnectionStats(io: SocketIOServer) {
  return {
    totalConnections: io.sockets.sockets.size,
    connectedClients: Array.from(io.sockets.sockets.keys()),
    serverUptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
  }
}
