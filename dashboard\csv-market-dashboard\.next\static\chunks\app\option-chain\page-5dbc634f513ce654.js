(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[403],{1887:function(e,t,a){Promise.resolve().then(a.bind(a,3047))},3047:function(e,t,a){"use strict";let r,o,n;a.r(t),a.d(t,{default:function(){return D}});var s=a(7437),c=a(2265),l=a(3448),i=a(9191);function d(e){let{marketData:t}=e,[a,r]=(0,c.useState)(null),[o,n]=(0,c.useState)(""),[d,m]=(0,c.useState)(null),[h,u]=(0,c.useState)(!1),[x,g]=(0,c.useState)(null),[p,y]=(0,c.useState)(0),[f,v]=(0,c.useState)(!1);(0,c.useEffect)(()=>{(async()=>{try{console.log("\uD83D\uDCD6 OptionChain: Loading cached data...");let e=await i.qn.getCachedStaticData(i.A_.OPTION_CHAIN);e&&"object"==typeof e&&"rows"in e&&(m(e),console.log("✅ OptionChain: Loaded option chain from cache"));let t=await i.qn.getCachedStaticData(i.A_.EXPIRY_DATES);t&&"object"==typeof t&&"expiries"in t&&Array.isArray(t.expiries)&&(r(t),t.expiries.length>0&&n(t.expiries[0]),console.log("✅ OptionChain: Loaded expiry data from cache"));let a=await i.qn.getCachedMarketData(i.A_.NIFTY_SPOT);a&&"object"==typeof a&&"ltp"in a&&"number"==typeof a.ltp&&(y(a.ltp),console.log("✅ OptionChain: Loaded NIFTY spot from cache:",a.ltp)),v(!0)}catch(e){console.error("❌ OptionChain: Failed to load cached data:",e),v(!0)}})()},[]),(0,c.useEffect)(()=>{let e=t.get("13");if(e&&e.ltp>0){y(e.ltp),i.qn.cacheMarketData(i.A_.NIFTY_SPOT,e),console.log("[SPOT] \uD83C\uDFAF NIFTY Spot Price Updated:",e.ltp,"from security ID 13 (IDX_I)");return}for(let[e,a]of Array.from(t.entries()))if("NIFTY"===a.symbol&&a.ltp>0){y(a.ltp),i.qn.cacheMarketData(i.A_.NIFTY_SPOT,a),console.log("[SPOT] \uD83C\uDFAF NIFTY Spot Price Found:",a.ltp,"from security:",e,"exchange:",a.exchange);return}0===p&&f&&(y(24850),console.warn("[SPOT] ⚠️ Using mock NIFTY Spot Price:",24850,"(INDEX data not available - check subscription)"))},[t,f]),(0,c.useEffect)(()=>{D()},[]),(0,c.useEffect)(()=>{o&&p>0&&S()},[o,p,t]);let D=async()=>{try{u(!0),g(null);let e=await i.qn.getCachedStaticData(i.A_.EXPIRY_DATES);if(e&&"object"==typeof e&&"expiries"in e&&Array.isArray(e.expiries)){console.log("✅ OptionChain: Using cached expiry data"),r(e),e.expiries.length>0&&n(e.expiries[0]),u(!1);return}console.log("\uD83C\uDF10 OptionChain: Fetching fresh expiry data from API");let t=await fetch("/api/nifty-expiry");if(!t.ok)throw Error("Failed to fetch expiry dates: ".concat(t.statusText));let a=await t.json();if(a.success)r(a.data),await i.qn.cacheStaticData(i.A_.EXPIRY_DATES,a.data),console.log("\uD83D\uDCBE OptionChain: Cached expiry data"),a.data.expiries.length>0&&n(a.data.expiries[0]);else throw Error(a.message||"Failed to fetch expiry dates")}catch(e){console.error("❌ OptionChain: Error fetching expiry dates:",e),g(e instanceof Error?e.message:"Unknown error")}finally{u(!1)}},S=()=>{if(!o||p<=0)return;console.log("\uD83D\uDD17 Building option chain for expiry:",o,"spot:",p);let e=b(p).map(e=>{let t=k(e,"CE"),a=k(e,"PE");return{strikePrice:e,call:t,put:a}}),t={underlying:"NIFTY",spotPrice:p,expiry:o,rows:e,timestamp:Date.now()};m(t),i.qn.cacheStaticData(i.A_.OPTION_CHAIN,t),console.log("\uD83D\uDCBE OptionChain: Cached option chain data for",o)},b=e=>{let a=new Set;for(let[e,r]of Array.from(t.entries()))if(r.symbol.includes("NIFTY")&&r.symbol.includes(N(o))){let e=r.symbol.split("-");if(e.length>=4){let t=parseFloat(e[2]);isNaN(t)||a.add(t)}}if(0===a.size){let t=[],a=50*Math.round(e/50);for(let e=-10;e<=10;e++)t.push(a+50*e);return t.sort((e,t)=>e-t)}let r=Array.from(a).sort((e,t)=>e-t),n=r.reduce((t,a)=>Math.abs(a-e)<Math.abs(t-e)?a:t);console.log("[STRIKE] \uD83C\uDFAF ATM Strike identified: ".concat(n," (Spot: ").concat(e,")")),console.log("[STRIKE] \uD83D\uDCCA Available strikes: ".concat(r.length));let s=r.indexOf(n),c=[...r.slice(Math.max(0,s-12),s),n,...r.slice(s+1,s+13)];return console.log("[STRIKE] ✅ Selected ".concat(c.length," strikes around ATM:"),c),c},k=(e,a)=>{for(let[h,u]of Array.from(t.entries()))if(u.symbol.includes("NIFTY-")&&u.symbol.includes("-".concat(e,"-").concat(a))&&u.expiryDate===o){var r,n,s,c,l,i,d,m;return console.log("[OPTION] ✅ Found ".concat(a," ").concat(e,": ").concat(u.symbol," (Expiry: ").concat(u.expiryDate,")")),{securityId:h,symbol:u.symbol,exchange:u.exchange,strikePrice:e,optionType:a,expiryDate:o,ltp:u.ltp||0,change:u.change||0,changePercent:u.changePercent||0,volume:u.volume||0,openInterest:u.openInterest,bid:(null===(n=u.marketDepth)||void 0===n?void 0:null===(r=n[0])||void 0===r?void 0:r.bidPrice)||u.bid,ask:(null===(c=u.marketDepth)||void 0===c?void 0:null===(s=c[0])||void 0===s?void 0:s.askPrice)||u.ask,bidQty:(null===(i=u.marketDepth)||void 0===i?void 0:null===(l=i[0])||void 0===l?void 0:l.bidQty)||u.bidQty,askQty:(null===(m=u.marketDepth)||void 0===m?void 0:null===(d=m[0])||void 0===d?void 0:d.askQty)||u.askQty,high:u.high,low:u.low,open:u.open,close:u.close,timestamp:u.timestamp}}return null},N=e=>{let t=new Date(e);return"".concat(["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][t.getMonth()]).concat(t.getFullYear())},j=l.A1.price,C=l.A1.number,w=e=>e?e>0?"text-green-400":e<0?"text-red-400":"text-gray-400":"text-gray-400";return h?(0,s.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading option chain..."})]})}):x?(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-red-800 font-medium",children:"Error Loading Option Chain"}),(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:x}),(0,s.jsx)("button",{onClick:D,className:"mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700",children:"Retry"})]}):(0,s.jsxs)("div",{className:"bg-white min-h-screen",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Options"}),(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md",children:"\uD83D\uDD0D NIFTY"}),(0,s.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Strategy builder"}),(0,s.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Class"}),(0,s.jsx)("button",{className:"px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md",children:"Volatility"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"By expiration | by strike"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"NIFTY Spot:"}),(0,s.jsx)("span",{className:"text-lg font-bold text-gray-900",children:j(p)}),24850===p&&(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"(Mock)"})]})]})]})}),a&&(0,s.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,s.jsx)("div",{className:"flex items-center space-x-2 overflow-x-auto pb-2",children:a.expiries.slice(0,15).map((e,t)=>{let a=new Date(e),r=e===o,c=a.toDateString()===new Date().toDateString(),l=6048e5>Math.abs(a.getTime()-new Date().getTime()),i=new Date().getFullYear(),d=a.getFullYear(),m=a.toLocaleDateString("en-US",{month:"short"}),h=a.getDate();return(0,s.jsx)("button",{onClick:()=>n(e),className:"flex-shrink-0 px-3 py-2 text-xs font-medium transition-colors border ".concat(r?"bg-black text-white border-black":c?"bg-orange-500 text-white border-orange-500":l?"bg-gray-100 text-gray-800 border-gray-300 hover:bg-gray-200":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:(0,s.jsxs)("div",{className:"text-center min-w-[40px]",children:[(0,s.jsxs)("div",{className:"text-xs font-normal text-gray-600",children:[m,d!==i&&" ".concat(d.toString().slice(-2))]}),(0,s.jsx)("div",{className:"font-bold text-sm",children:h})]})},e)})}),!1]}),d&&(0,s.jsxs)("div",{className:"bg-white",children:[(0,s.jsxs)("div",{className:"flex bg-gray-50 border-b border-gray-200 text-xs font-medium text-gray-700 uppercase tracking-wider",children:[(0,s.jsx)("div",{className:"flex-1 text-center py-3 border-r border-gray-200",children:(0,s.jsx)("span",{className:"text-green-600 font-bold",children:"Calls"})}),(0,s.jsx)("div",{className:"w-20 text-center py-3 border-r border-gray-200",children:(0,s.jsx)("span",{className:"font-bold",children:"Strike"})}),(0,s.jsx)("div",{className:"flex-1 text-center py-3",children:(0,s.jsx)("span",{className:"text-red-600 font-bold",children:"Puts"})})]}),(0,s.jsxs)("div",{className:"flex bg-gray-100 border-b border-gray-200 text-xs font-medium text-gray-600 uppercase tracking-wider",children:[(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"OI"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1 border-r border-gray-200",children:"LTP"}),(0,s.jsx)("div",{className:"w-20 text-center py-2 px-1 border-r border-gray-200 font-bold",children:"Strike"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"LTP"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Change"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Ask"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Bid"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"Volume"}),(0,s.jsx)("div",{className:"flex-1 text-center py-2 px-1",children:"OI"})]}),(0,s.jsx)("div",{className:"divide-y divide-gray-100",children:d.rows.map((e,t)=>{var a,r,o,n,c,l,i,d,m,h,u,x,g,y;let f=50>=Math.abs(e.strikePrice-p),v=e.strikePrice<p,D=e.strikePrice>p;return(0,s.jsxs)("div",{className:"flex hover:bg-gray-50 transition-colors ".concat(f?"bg-yellow-50":t%2==0?"bg-white":"bg-gray-25"),children:[(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(v?"text-green-700 font-medium":"text-gray-700"),children:C(null===(a=e.call)||void 0===a?void 0:a.openInterest)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(v?"text-green-700 font-medium":"text-gray-700"),children:C(null===(r=e.call)||void 0===r?void 0:r.volume)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:j(null===(o=e.call)||void 0===o?void 0:o.bid)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:j(null===(n=e.call)||void 0===n?void 0:n.ask)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-medium ".concat(w(null===(c=e.call)||void 0===c?void 0:c.change)),children:(null===(l=e.call)||void 0===l?void 0:l.change)?(0,s.jsxs)(s.Fragment,{children:[e.call.change>0?"+":"",e.call.change.toFixed(2),(0,s.jsxs)("div",{className:"text-xs",children:["(",e.call.changePercent>0?"+":"",e.call.changePercent.toFixed(1),"%)"]})]}):"-"}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ".concat(v?"text-green-600":"text-gray-700"),children:j(null===(i=e.call)||void 0===i?void 0:i.ltp)}),(0,s.jsx)("div",{className:"w-20 text-center py-3 px-1 text-sm font-bold border-r border-gray-200 ".concat(f?"bg-yellow-100 text-yellow-800":"text-gray-900"),children:e.strikePrice}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-bold ".concat(D?"text-red-600":"text-gray-700"),children:j(null===(d=e.put)||void 0===d?void 0:d.ltp)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm font-medium ".concat(w(null===(m=e.put)||void 0===m?void 0:m.change)),children:(null===(h=e.put)||void 0===h?void 0:h.change)?(0,s.jsxs)(s.Fragment,{children:[e.put.change>0?"+":"",e.put.change.toFixed(2),(0,s.jsxs)("div",{className:"text-xs",children:["(",e.put.changePercent>0?"+":"",e.put.changePercent.toFixed(1),"%)"]})]}):"-"}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-red-600",children:j(null===(u=e.put)||void 0===u?void 0:u.ask)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm text-green-600",children:j(null===(x=e.put)||void 0===x?void 0:x.bid)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(D?"text-red-700 font-medium":"text-gray-700"),children:C(null===(g=e.put)||void 0===g?void 0:g.volume)}),(0,s.jsx)("div",{className:"flex-1 text-center py-3 px-1 text-sm ".concat(D?"text-red-700 font-medium":"text-gray-700"),children:C(null===(y=e.put)||void 0===y?void 0:y.openInterest)})]},e.strikePrice)})})]}),d&&(0,s.jsx)("div",{className:"bg-gray-50 border-t border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:[(0,s.jsx)("span",{children:"Show all"}),(0,s.jsx)("span",{children:"•"}),(0,s.jsxs)("span",{children:["Showing ",d.rows.length," strikes around ATM"]})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(d.timestamp).toLocaleTimeString()]})]})})]})}let m=e=>{let t;let a=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),a.forEach(a=>a(t,e))}},o=()=>t,n={setState:r,getState:o,getInitialState:()=>s,subscribe:e=>(a.add(e),()=>a.delete(e))},s=t=e(r,o,n);return n},h=e=>e?m(e):m,u=e=>e,x=e=>{let t=h(e),a=e=>(function(e,t=u){let a=c.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return c.useDebugValue(a),a})(t,e);return Object.assign(a,t),a};function g(e,t){let a;try{a=e()}catch(e){return}return{getItem:e=>{var r;let o=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),n=null!=(r=a.getItem(e))?r:null;return n instanceof Promise?n.then(o):o(n)},setItem:(e,r)=>a.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>a.removeItem(e)}}let p=e=>t=>{try{let a=e(t);if(a instanceof Promise)return a;return{then:e=>p(e)(a),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>p(t)(e)}}};var y=a(5052);let f=(r?x(r):x)((o=(e,t)=>({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1,wsManager:null,setMarketData:a=>{e({marketData:a.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:new Date,isLoading:!1}),t().saveToLocalStorage()},updateMarketData:t=>e(e=>({marketData:{...e.marketData,[t.securityId]:{...e.marketData[t.securityId],...t}}})),updateMarketDataBatch:t=>e(e=>{let a={...e.marketData};for(let e of t)a[e.securityId]={...a[e.securityId],...e};return{marketData:a}}),hydrateFromRedis:t=>e({marketData:t.reduce((e,t)=>(e[t.securityId]=t,e),{})}),setConnectionStatus:a=>{e({connectionStatus:a,isConnected:"connected"===a,error:"error"===a?t().error:null})},setError:t=>e({error:t}),setLoading:t=>e({isLoading:t}),setCacheLoaded:t=>e({cacheLoaded:t}),initializeWebSocket:()=>{let a=y.ZP.getInstance();e({wsManager:a}),a.connect({onConnect:()=>{console.log("\uD83D\uDD0C MarketStore: WebSocket connected"),t().setConnectionStatus("connected")},onDisconnect:e=>{console.log("\uD83D\uDD0C MarketStore: WebSocket disconnected:",e),t().setConnectionStatus("disconnected")},onError:e=>{console.error("❌ MarketStore: WebSocket error:",e),t().setConnectionStatus("error"),t().setError(e.message||"WebSocket connection error")},onReconnect:e=>{console.log("\uD83D\uDD04 MarketStore: WebSocket reconnected after",e,"attempts"),t().setConnectionStatus("connected"),t().setError(null)},onMarketData:e=>{e&&"object"==typeof e&&t().updateMarketData(e)},onMarketDataBatch:e=>{Array.isArray(e)&&e.length>0&&t().updateMarketDataBatch(e)}})},connect:()=>{let{wsManager:e}=t();e&&!e.isConnected()&&(t().setConnectionStatus("connecting"),console.log("\uD83D\uDD0C MarketStore: Connection request - WebSocket manager will handle"))},disconnect:()=>{let{wsManager:e}=t();e&&(e.disconnect(),t().setConnectionStatus("disconnected"))},loadFromCache:async()=>{try{e({isLoading:!0}),console.log("\uD83D\uDCD6 MarketStore: Loading cached market data...");let a=t().loadFromLocalStorage(),r=await fetch("/api/cache/bulk");if(r.ok){let e=await r.json();e.success&&e.data&&Array.isArray(e.data)&&e.data.length>0?(t().setMarketData(e.data),console.log("✅ MarketStore: Loaded ".concat(e.data.length," items from Redis cache"))):a||console.log("\uD83D\uDCED MarketStore: No cached data found")}else a||console.log("\uD83D\uDCED MarketStore: Failed to load cached data");e({cacheLoaded:!0,isLoading:!1})}catch(t){console.error("❌ MarketStore: Failed to load cached data:",t),e({cacheLoaded:!0,isLoading:!1})}},refreshFromCache:async()=>{try{let e=await fetch("/api/cache/bulk");if(e.ok){let a=await e.json();a.success&&a.data&&Array.isArray(a.data)&&a.data.length>0&&(t().setMarketData(a.data),console.log("✅ MarketStore: Refreshed ".concat(a.data.length," items from cache")))}}catch(e){console.error("❌ MarketStore: Failed to refresh from cache:",e)}},clearCache:async()=>{try{(await fetch("/api/cache/clear",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pattern:"market_data:*"})})).ok&&(e({marketData:{},lastUpdate:null}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp"),console.log("\uD83E\uDDF9 MarketStore: Cache cleared"))}catch(e){console.error("❌ MarketStore: Failed to clear cache:",e)}},saveToLocalStorage:()=>{let{marketData:e}=t();if(Object.keys(e).length>0)try{localStorage.setItem("marketData",JSON.stringify(Object.values(e))),localStorage.setItem("marketDataTimestamp",new Date().toISOString())}catch(e){console.warn("⚠️ MarketStore: Failed to save to localStorage:",e)}},loadFromLocalStorage:()=>{try{let t=localStorage.getItem("marketData"),a=localStorage.getItem("marketDataTimestamp");if(t&&a){let r=JSON.parse(t),o=new Date(a),n=(new Date().getTime()-o.getTime())/6e4;if(n<10&&Array.isArray(r)&&r.length>0)return e({marketData:r.reduce((e,t)=>(e[t.securityId]=t,e),{}),lastUpdate:o,isLoading:!1}),console.log("⚡ MarketStore: Loaded ".concat(r.length," items from localStorage (").concat(n.toFixed(1),"min old)")),!0}}catch(e){console.warn("⚠️ MarketStore: Failed to load from localStorage:",e),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")}return!1},reset:()=>{e({marketData:{},isConnected:!1,isLoading:!0,lastUpdate:null,connectionStatus:"disconnected",error:null,cacheLoaded:!1}),localStorage.removeItem("marketData"),localStorage.removeItem("marketDataTimestamp")},getMarketDataBySymbol:e=>Object.values(t().marketData).find(t=>t.symbol===e),getMarketDataBySecurityId:e=>t().marketData[e]}),n={name:"market-store",storage:g(()=>localStorage),partialize:e=>({marketData:e.marketData})},(e,t,a)=>{let r,s={storage:g(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...n},c=!1,l=new Set,i=new Set,d=s.storage;if(!d)return o((...t)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),e(...t)},t,a);let m=()=>{let e=s.partialize({...t()});return d.setItem(s.name,{state:e,version:s.version})},h=a.setState;a.setState=(e,t)=>{h(e,t),m()};let u=o((...t)=>{e(...t),m()},t,a);a.getInitialState=()=>u;let x=()=>{var a,o;if(!d)return;c=!1,l.forEach(e=>{var a;return e(null!=(a=t())?a:u)});let n=(null==(o=s.onRehydrateStorage)?void 0:o.call(s,null!=(a=t())?a:u))||void 0;return p(d.getItem.bind(d))(s.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===s.version)return[!1,e.state];if(s.migrate){let t=s.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(a=>{var o;let[n,c]=a;if(e(r=s.merge(c,null!=(o=t())?o:u),!0),n)return m()}).then(()=>{null==n||n(r,void 0),r=t(),c=!0,i.forEach(e=>e(r))}).catch(e=>{null==n||n(void 0,e)})};return a.persist={setOptions:e=>{s={...s,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>x(),hasHydrated:()=>c,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(i.add(e),()=>{i.delete(e)})},s.skipHydration||x(),r||u})),v=()=>{let e=f();return(0,c.useEffect)(()=>{fetch("/api/cache/all-latest").then(e=>e.json()).then(t=>{let{data:a}=t;Array.isArray(a)&&e.hydrateFromRedis(a)})},[e.hydrateFromRedis]),{marketData:Object.values(e.marketData),updateMarketData:e.updateMarketData,updateMarketDataBatch:e.updateMarketDataBatch,hydrateFromRedis:e.hydrateFromRedis}};function D(){let{marketData:e}=v(),t=new Map;return e.forEach(e=>{e.securityId&&t.set(e.securityId,e)}),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("a",{href:"/",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"← Main Dashboard"}),(0,s.jsx)("a",{href:"/subscribed",className:"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 rounded-md hover:bg-gray-100 transition-colors",children:"\uD83D\uDCCA Subscribed Data"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," instruments"]}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[e.filter(e=>{var t;return(null!==(t=e.ltp)&&void 0!==t?t:0)>0}).length," active"]})]})]})}),(0,s.jsx)(d,{marketData:t})]})}},9191:function(e,t,a){"use strict";a.d(t,{A_:function(){return s},qn:function(){return c}});var r=a(8705);class o{isBrowser(){return void 0!==window.localStorage}static getInstance(){return o.instance||(o.instance=new o),o.instance}initializeClientSide(){this.isBrowser()&&(console.log("\uD83D\uDE80 DataCache: Initializing client-side cache"),this.cleanupExpired())}checkCompressionSupport(){try{this.compressionSupported="undefined"!=typeof CompressionStream}catch(e){this.compressionSupported=!1}}async set(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(!this.isBrowser())return console.log("⚠️ DataCache: Skipping cache on server side for ".concat(e)),!1;let{ttl:o=r.ej.DEFAULT_TTL,useCompression:n=!1,storage:s="localStorage"}=a,c={data:t,timestamp:Date.now(),ttl:o,version:this.version},l=JSON.stringify(c);n&&this.compressionSupported&&(l=await this.compress(l));let i="localStorage"===s?localStorage:sessionStorage,d=this.getFullKey(e);return i.setItem(d,l),console.log("\uD83D\uDCBE DataCache: Cached ".concat(e," (").concat(l.length," bytes)")),!0}catch(t){return console.error("❌ DataCache: Failed to cache ".concat(e,":"),t),!1}}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return null;let{storage:a="localStorage"}=t,r="localStorage"===a?localStorage:sessionStorage,o=this.getFullKey(e),n=r.getItem(o);if(!n)return null;let s=n;this.compressionSupported&&this.isCompressed(n)&&(s=await this.decompress(n));let c=JSON.parse(s);if(c.version!==this.version)return console.warn("⚠️ DataCache: Version mismatch for ".concat(e,", removing")),this.remove(e,t),null;if(Date.now()-c.timestamp>c.ttl)return console.log("⏰ DataCache: ".concat(e," expired, removing")),this.remove(e,t),null;return console.log("\uD83D\uDCD6 DataCache: Retrieved ".concat(e," from cache")),c.data}catch(a){return console.error("❌ DataCache: Failed to retrieve ".concat(e,":"),a),this.remove(e,t),null}}remove(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{if(!this.isBrowser())return;let{storage:a="localStorage"}=t,r="localStorage"===a?localStorage:sessionStorage,o=this.getFullKey(e);r.removeItem(o),console.log("\uD83D\uDDD1️ DataCache: Removed ".concat(e))}catch(t){console.error("❌ DataCache: Failed to remove ".concat(e,":"),t)}}clear(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";try{if(!this.isBrowser())return;let t="localStorage"===e?localStorage:sessionStorage,a=[];for(let e=0;e<t.length;e++){let r=t.key(e);r&&r.startsWith("csv_market_dashboard_cache_")&&a.push(r)}a.forEach(e=>t.removeItem(e)),console.log("\uD83E\uDDF9 DataCache: Cleared ".concat(a.length," entries from ").concat(e))}catch(t){console.error("❌ DataCache: Failed to clear ".concat(e,":"),t)}}getStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"localStorage";if(!this.isBrowser())return{totalEntries:0,totalSize:0,oldestEntry:null,newestEntry:null};let t="localStorage"===e?localStorage:sessionStorage,a=0,r=0,o=1/0,n=0;try{for(let e=0;e<t.length;e++){let s=t.key(e);if(s&&s.startsWith("csv_market_dashboard_cache_")){let e=t.getItem(s);if(e){a++,r+=e.length;try{let t=JSON.parse(e);t.timestamp&&(o=Math.min(o,t.timestamp),n=Math.max(n,t.timestamp))}catch(e){}}}}}catch(e){console.error("❌ DataCache: Failed to get stats:",e)}return{totalEntries:a,totalSize:r,oldestEntry:o===1/0?null:new Date(o),newestEntry:0===n?null:new Date(n)}}startCleanupInterval(){this.isBrowser()&&setInterval(()=>{this.cleanupExpired()},r.ej.EXPIRY_CHECK_INTERVAL)}cleanupExpired(){try{if(!this.isBrowser())return;["localStorage","sessionStorage"].forEach(e=>{let t="localStorage"===e?localStorage:sessionStorage,a=[];for(let e=0;e<t.length;e++){let r=t.key(e);if(r&&r.startsWith("csv_market_dashboard_cache_"))try{let e=t.getItem(r);if(e){let t=JSON.parse(e);Date.now()-t.timestamp>t.ttl&&a.push(r)}}catch(e){a.push(r)}}a.forEach(e=>t.removeItem(e)),a.length>0&&console.log("\uD83E\uDDF9 DataCache: Cleaned up ".concat(a.length," expired entries from ").concat(e))})}catch(e){console.error("❌ DataCache: Cleanup failed:",e)}}getFullKey(e){return"csv_market_dashboard_cache_".concat(e)}isCompressed(e){return e.startsWith("H4sI")||e.startsWith("eJy")}async compress(e){return e}async decompress(e){return e}constructor(){this.version="1.0.0",this.compressionSupported=!1,this.checkCompressionSupport(),this.startCleanupInterval()}}o.instance=null;let n=o.getInstance(),s=r.ej.KEYS,c={cacheMarketData:async(e,t)=>n.set(e,t,{ttl:r.ej.MARKET_DATA_TTL,storage:"localStorage"}),cacheStaticData:async(e,t)=>n.set(e,t,{ttl:r.ej.STATIC_DATA_TTL,storage:"localStorage"}),getCachedMarketData:async e=>n.get(e,{storage:"localStorage"}),getCachedStaticData:async e=>n.get(e,{storage:"localStorage"})}}},function(e){e.O(0,[324,869,971,117,744],function(){return e(e.s=1887)}),_N_E=e.O()}]);