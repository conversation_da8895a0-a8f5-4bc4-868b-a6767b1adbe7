exports.id=432,exports.ids=[432],exports.modules={5549:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(8995)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},8995:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7914),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},614:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(5549):e.exports=s(2839)},2839:(e,t,s)=>{let r=s(4175),i=s(1764);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(7495);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(8995)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},1665:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:r}=s(7075),i=Buffer[Symbol.species];function n(e,t,s,r,i){for(let n=0;n<i;n++)s[r+n]=e[n]^t[3&n]}function o(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),n=0;for(let t=0;t<e.length;t++){let r=e[t];s.set(r,n),n+=r.length}return n<t?new i(s.buffer,s.byteOffset,n):s},mask:n,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new i(t):ArrayBuffer.isView(t)?s=new i(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(6804);e.exports.mask=function(e,s,r,i,o){o<48?n(e,s,r,i,o):t.mask(e,s,r,i,o)},e.exports.unmask=function(e,s){e.length<32?o(e,s):t.unmask(e,s)}}catch(e){}},7075:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},7967:(e,t,s)=>{"use strict";let{kForOnEventAttribute:r,kListener:i}=s(7075),n=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),c=Symbol("kMessage"),h=Symbol("kReason"),l=Symbol("kTarget"),u=Symbol("kType"),d=Symbol("kWasClean");class p{constructor(e){this[l]=null,this[u]=e}get target(){return this[l]}get type(){return this[u]}}Object.defineProperty(p.prototype,"target",{enumerable:!0}),Object.defineProperty(p.prototype,"type",{enumerable:!0});class f extends p{constructor(e,t={}){super(e),this[n]=void 0===t.code?0:t.code,this[h]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[n]}get reason(){return this[h]}get wasClean(){return this[d]}}Object.defineProperty(f.prototype,"code",{enumerable:!0}),Object.defineProperty(f.prototype,"reason",{enumerable:!0}),Object.defineProperty(f.prototype,"wasClean",{enumerable:!0});class g extends p{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[c]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[c]}}Object.defineProperty(g.prototype,"error",{enumerable:!0}),Object.defineProperty(g.prototype,"message",{enumerable:!0});class m extends p{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function _(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(m.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:f,ErrorEvent:g,Event:p,EventTarget:{addEventListener(e,t,s={}){let n;for(let n of this.listeners(e))if(!s[r]&&n[i]===t&&!n[r])return;if("message"===e)n=function(e,s){let r=new m("message",{data:s?e:e.toString()});r[l]=this,_(t,this,r)};else if("close"===e)n=function(e,s){let r=new f("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[l]=this,_(t,this,r)};else if("error"===e)n=function(e){let s=new g("error",{error:e,message:e.message});s[l]=this,_(t,this,s)};else{if("open"!==e)return;n=function(){let e=new p("open");e[l]=this,_(t,this,e)}}n[r]=!!s[r],n[i]=t,s.once?this.once(e,n):this.on(e,n)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[i]===t&&!s[r]){this.removeListener(e,s);break}}},MessageEvent:m}},2485:(e,t,s)=>{"use strict";let{tokenChars:r}=s(4245);function i(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let n=Object.create(null),o=Object.create(null),a=!1,c=!1,h=!1,l=-1,u=-1,d=-1,p=0;for(;p<e.length;p++)if(u=e.charCodeAt(p),void 0===t){if(-1===d&&1===r[u])-1===l&&(l=p);else if(0!==p&&(32===u||9===u))-1===d&&-1!==l&&(d=p);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p);let s=e.slice(l,d);44===u?(i(n,s,o),o=Object.create(null)):t=s,l=d=-1}else throw SyntaxError(`Unexpected character at index ${p}`)}else if(void 0===s){if(-1===d&&1===r[u])-1===l&&(l=p);else if(32===u||9===u)-1===d&&-1!==l&&(d=p);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p),i(o,e.slice(l,d),!0),44===u&&(i(n,t,o),o=Object.create(null),t=void 0),l=d=-1}else if(61===u&&-1!==l&&-1===d)s=e.slice(l,p),l=d=-1;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(c){if(1!==r[u])throw SyntaxError(`Unexpected character at index ${p}`);-1===l?l=p:a||(a=!0),c=!1}else if(h){if(1===r[u])-1===l&&(l=p);else if(34===u&&-1!==l)h=!1,d=p;else if(92===u)c=!0;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(34===u&&61===e.charCodeAt(p-1))h=!0;else if(-1===d&&1===r[u])-1===l&&(l=p);else if(-1!==l&&(32===u||9===u))-1===d&&(d=p);else if(59===u||44===u){if(-1===l)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p);let r=e.slice(l,d);a&&(r=r.replace(/\\/g,""),a=!1),i(o,s,r),44===u&&(i(n,t,o),o=Object.create(null),t=void 0),s=void 0,l=d=-1}else throw SyntaxError(`Unexpected character at index ${p}`);if(-1===l||h||32===u||9===u)throw SyntaxError("Unexpected end of input");-1===d&&(d=p);let f=e.slice(l,d);return void 0===t?i(n,f,o):(void 0===s?i(o,f,!0):a?i(o,s,f.replace(/\\/g,"")):i(o,s,f),i(n,t,o)),n}}},3700:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},5154:(e,t,s)=>{"use strict";let r;let i=s(1568),n=s(1665),o=s(3700),{kStatusCode:a}=s(7075),c=Buffer[Symbol.species],h=Buffer.from([0,0,255,255]),l=Symbol("permessage-deflate"),u=Symbol("total-length"),d=Symbol("callback"),p=Symbol("buffers"),f=Symbol("error");class g{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),s(e,t)})})}compress(e,t,s){r.add(r=>{this._compress(e,t,(e,t)=>{r(),s(e,t)})})}_decompress(e,t,s){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[l]=this,this._inflate[u]=0,this._inflate[p]=[],this._inflate.on("error",y),this._inflate.on("data",_)}this._inflate[d]=s,this._inflate.write(e),t&&this._inflate.write(h),this._inflate.flush(()=>{let e=this._inflate[f];if(e){this._inflate.close(),this._inflate=null,s(e);return}let i=n.concat(this._inflate[p],this._inflate[u]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[u]=0,this._inflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),s(null,i)})}_compress(e,t,s){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[u]=0,this._deflate[p]=[],this._deflate.on("data",m)}this._deflate[d]=s,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=n.concat(this._deflate[p],this._deflate[u]);t&&(e=new c(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[u]=0,this._deflate[p]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function m(e){this[p].push(e),this[u]+=e.length}function _(e){if(this[u]+=e.length,this[l]._maxPayload<1||this[u]<=this[l]._maxPayload){this[p].push(e);return}this[f]=RangeError("Max payload size exceeded"),this[f].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[f][a]=1009,this.removeListener("data",_),this.reset()}function y(e){this[l]._inflate=null,e[a]=1007,this[d](e)}e.exports=g},6292:(e,t,s)=>{"use strict";let{Writable:r}=s(6162),i=s(5154),{BINARY_TYPES:n,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:c}=s(7075),{concat:h,toArrayBuffer:l,unmask:u}=s(1665),{isValidStatusCode:d,isValidUTF8:p}=s(4245),f=Buffer[Symbol.species];class g extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||n[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[c]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new f(t.buffer,t.byteOffset+e,t.length-e),new f(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new f(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[i.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=4294967296*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&u(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?h(s,t):"arraybuffer"===this._binaryType?l(h(s,t)):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=h(s,t);if(!this._skipUTF8Validation&&!p(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let s=e.readUInt16BE(0);if(!d(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new f(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!p(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,i){this._loop=!1,this._errored=!0;let n=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(n,this.createError),n.code=i,n[a]=r,n}}e.exports=g},4272:(e,t,s)=>{"use strict";let r;let{Duplex:i}=s(6162),{randomFillSync:n}=s(4770),o=s(5154),{EMPTY_BUFFER:a}=s(7075),{isValidStatusCode:c}=s(4245),{mask:h,toBuffer:l}=s(1665),u=Symbol("kByteLength"),d=Buffer.alloc(4),p=8192;class f{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let s,i;let o=!1,a=2,c=!1;t.mask&&(s=t.maskBuffer||d,t.generateMask?t.generateMask(s):(8192===p&&(void 0===r&&(r=Buffer.alloc(8192)),n(r,0,8192),p=0),s[0]=r[p++],s[1]=r[p++],s[2]=r[p++],s[3]=r[p++]),c=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?i=(!t.mask||c)&&void 0!==t[u]?t[u]:(e=Buffer.from(e)).length:(i=e.length,o=t.mask&&t.readOnly&&!c);let l=i;i>=65536?(a+=8,l=127):i>125&&(a+=2,l=126);let f=Buffer.allocUnsafe(o?i+a:a);return(f[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(f[0]|=64),f[1]=l,126===l?f.writeUInt16BE(i,2):127===l&&(f[2]=f[3]=0,f.writeUIntBE(i,4,6)),t.mask)?(f[1]|=128,f[a-4]=s[0],f[a-3]=s[1],f[a-2]=s[2],f[a-1]=s[3],c)?[f,e]:o?(h(e,s,f,a,i),[f]):(h(e,s,e,0,i),[f,e]):[f,e]}close(e,t,s,r){let i;if(void 0===e)i=a;else if("number"==typeof e&&c(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let n={[u]:i.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,i,!1,n,r]):this.sendFrame(f.frame(i,n),r)}ping(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=l(e)).length,i=l.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(f.frame(e,n),s)}pong(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=l(e)).length,i=l.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let n={[u]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,n,s]):this.sendFrame(f.frame(e,n),s)}send(e,t,s){let r,i;let n=this._extensions[o.extensionName],a=t.binary?2:1,c=t.compress;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):(r=(e=l(e)).length,i=l.readOnly),this._firstFragment?(this._firstFragment=!1,c&&n&&n.params[n._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(c=r>=n._threshold),this._compress=c):(c=!1,a=0),t.fin&&(this._firstFragment=!0),n){let n={[u]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:c};this._deflating?this.enqueue([this.dispatch,e,this._compress,n,s]):this.dispatch(e,this._compress,n,s)}else this.sendFrame(f.frame(e,{[u]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:!1}),s)}dispatch(e,t,s,r){if(!t){this.sendFrame(f.frame(e,s),r);return}let i=this._extensions[o.extensionName];this._bufferedBytes+=s[u],this._deflating=!0,i.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof r&&r(e);for(let t=0;t<this._queue.length;t++){let s=this._queue[t],r=s[s.length-1];"function"==typeof r&&r(e)}return}this._bufferedBytes-=s[u],this._deflating=!1,s.readOnly=!1,this.sendFrame(f.frame(t,s),r),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][u],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][u],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=f},14:(e,t,s)=>{"use strict";let{Duplex:r}=s(6162);function i(e){e.emit("close")}function n(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(s=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(i,a);return}let n=!1;e.once("error",function(e){n=!0,r(e)}),e.once("close",function(){n||r(t),process.nextTick(i,a)}),s&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,s,r)});return}e.send(t,r)},a.on("end",n),a.on("error",o),a}},5348:(e,t,s)=>{"use strict";let{tokenChars:r}=s(4245);e.exports={parse:function(e){let t=new Set,s=-1,i=-1,n=0;for(;n<e.length;n++){let o=e.charCodeAt(n);if(-1===i&&1===r[o])-1===s&&(s=n);else if(0!==n&&(32===o||9===o))-1===i&&-1!==s&&(i=n);else if(44===o){if(-1===s)throw SyntaxError(`Unexpected character at index ${n}`);-1===i&&(i=n);let r=e.slice(s,i);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),s=i=-1}else throw SyntaxError(`Unexpected character at index ${n}`)}if(-1===s||-1!==i)throw SyntaxError("Unexpected end of input");let o=e.slice(s,n);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},4245:(e,t,s)=>{"use strict";let{isUtf8:r}=s(8893);function i(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?i(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(8645);e.exports.isValidUTF8=function(e){return e.length<32?i(e):t(e)}}catch(e){}},7203:(e,t,s)=>{"use strict";let r=s(7702),i=s(2615),{Duplex:n}=s(6162),{createHash:o}=s(4770),a=s(2485),c=s(5154),h=s(5348),l=s(608),{GUID:u,kWebSocket:d}=s(7075),p=/^[+/0-9A-Za-z]{22}==$/;class f extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:l,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=i.createServer((e,t)=>{let s=i.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(g,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(g,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{g(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",m);let i=e.headers["sec-websocket-key"],n=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){y(this,e,t,405,"Invalid HTTP method");return}if(void 0===n||"websocket"!==n.toLowerCase()){y(this,e,t,400,"Invalid Upgrade header");return}if(void 0===i||!p.test(i)){y(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){y(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){_(t,400);return}let l=e.headers["sec-websocket-protocol"],u=new Set;if(void 0!==l)try{u=h.parse(l)}catch(s){y(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let d=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&void 0!==d){let s=new c(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(d);e[c.extensionName]&&(s.accept(e[c.extensionName]),f[c.extensionName]=s)}catch(s){y(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let n={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(n,(n,o,a,c)=>{if(!n)return _(t,o||401,a,c);this.completeUpgrade(f,i,u,e,t,s,r)});return}if(!this.options.verifyClient(n))return _(t,401)}this.completeUpgrade(f,i,u,e,t,s,r)}completeUpgrade(e,t,s,r,i,n,h){if(!i.readable||!i.writable)return i.destroy();if(i[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return _(i,503);let l=o("sha1").update(t+u).digest("base64"),p=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${l}`],f=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(p.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[c.extensionName]){let t=e[c.extensionName].params,s=a.format({[c.extensionName]:[t]});p.push(`Sec-WebSocket-Extensions: ${s}`),f._extensions=e}this.emit("headers",p,r),i.write(p.concat("\r\n").join("\r\n")),i.removeListener("error",m),f.setSocket(i,n,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(g,this)})),h(f,r)}}function g(e){e._state=2,e.emit("close")}function m(){this.destroy()}function _(e,t,s,r){s=s||i.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${i.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+s)}function y(e,t,s,r,i){if(e.listenerCount("wsClientError")){let r=Error(i);Error.captureStackTrace(r,y),e.emit("wsClientError",r,s,t)}else _(s,r,i)}e.exports=f},608:(e,t,s)=>{"use strict";let r=s(7702),i=s(8791),n=s(2615),o=s(8216),a=s(2452),{randomBytes:c,createHash:h}=s(4770),{Duplex:l,Readable:u}=s(6162),{URL:d}=s(7360),p=s(5154),f=s(6292),g=s(4272),{BINARY_TYPES:m,EMPTY_BUFFER:_,GUID:y,kForOnEventAttribute:b,kListener:C,kStatusCode:v,kWebSocket:E,NOOP:w}=s(7075),{EventTarget:{addEventListener:k,removeEventListener:S}}=s(7967),{format:x,parse:O}=s(2485),{toBuffer:T}=s(1665),F=Symbol("kAborted"),N=[8,13],R=["CONNECTING","OPEN","CLOSING","CLOSED"],L=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class A extends r{constructor(e,t,s){super(),this._binaryType=m[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=_,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=A.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,r,o){let a,l,u,f;let g={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:N[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=g.autoPong,!N.includes(g.protocolVersion))throw RangeError(`Unsupported protocol version: ${g.protocolVersion} (supported versions: ${N.join(", ")})`);if(s instanceof d)a=s;else try{a=new d(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let m="wss:"===a.protocol,_="ws+unix:"===a.protocol;if("ws:"===a.protocol||m||_?_&&!a.pathname?l="The URL's pathname is empty":a.hash&&(l="The URL contains a fragment identifier"):l='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',l){let e=SyntaxError(l);if(0===t._redirects)throw e;B(t,e);return}let b=m?443:80,C=c(16).toString("base64"),v=m?i.request:n.request,E=new Set;if(g.createConnection=g.createConnection||(m?P:I),g.defaultPort=g.defaultPort||b,g.port=a.port||b,g.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,g.headers={...g.headers,"Sec-WebSocket-Version":g.protocolVersion,"Sec-WebSocket-Key":C,Connection:"Upgrade",Upgrade:"websocket"},g.path=a.pathname+a.search,g.timeout=g.handshakeTimeout,g.perMessageDeflate&&(u=new p(!0!==g.perMessageDeflate?g.perMessageDeflate:{},!1,g.maxPayload),g.headers["Sec-WebSocket-Extensions"]=x({[p.extensionName]:u.offer()})),r.length){for(let e of r){if("string"!=typeof e||!L.test(e)||E.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");E.add(e)}g.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(g.origin&&(g.protocolVersion<13?g.headers["Sec-WebSocket-Origin"]=g.origin:g.headers.Origin=g.origin),(a.username||a.password)&&(g.auth=`${a.username}:${a.password}`),_){let e=g.path.split(":");g.socketPath=e[0],g.path=e[1]}if(g.followRedirects){if(0===t._redirects){t._originalIpc=_,t._originalSecure=m,t._originalHostOrSocketPath=_?g.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,s]of Object.entries(e))o.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=_?!!t._originalIpc&&g.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||m)||(delete g.headers.authorization,delete g.headers.cookie,e||delete g.headers.host,g.auth=void 0)}g.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(g.auth).toString("base64")),f=t._req=v(g),t._redirects&&t.emit("redirect",t.url,f)}else f=t._req=v(g);g.timeout&&f.on("timeout",()=>{D(t,f,"Opening handshake has timed out")}),f.on("error",e=>{null===f||f[F]||(f=t._req=null,B(t,e))}),f.on("response",i=>{let n=i.headers.location,a=i.statusCode;if(n&&g.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>g.maxRedirects){D(t,f,"Maximum redirects exceeded");return}f.abort();try{i=new d(n,s)}catch(e){B(t,SyntaxError(`Invalid URL: ${n}`));return}e(t,i,r,o)}else t.emit("unexpected-response",f,i)||D(t,f,`Unexpected server response: ${i.statusCode}`)}),f.on("upgrade",(e,s,r)=>{let i;if(t.emit("upgrade",e),t.readyState!==A.CONNECTING)return;f=t._req=null;let n=e.headers.upgrade;if(void 0===n||"websocket"!==n.toLowerCase()){D(t,s,"Invalid Upgrade header");return}let o=h("sha1").update(C+y).digest("base64");if(e.headers["sec-websocket-accept"]!==o){D(t,s,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?E.size?E.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":E.size&&(i="Server sent no subprotocol"),i){D(t,s,i);return}a&&(t._protocol=a);let c=e.headers["sec-websocket-extensions"];if(void 0!==c){let e;if(!u){D(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=O(c)}catch(e){D(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==p.extensionName){D(t,s,"Server indicated an extension that was not requested");return}try{u.accept(e[p.extensionName])}catch(e){D(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=u}t.setSocket(s,r,{allowSynchronousEvents:g.allowSynchronousEvents,generateMask:g.generateMask,maxPayload:g.maxPayload,skipUTF8Validation:g.skipUTF8Validation})}),g.finishRequest?g.finishRequest(f,t):f.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){m.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let r=new f({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation});this._sender=new g(e,this._extensions,s.generateMask),this._receiver=r,this._socket=e,r[E]=this,e[E]=this,r.on("conclude",j),r.on("drain",M),r.on("error",q),r.on("message",W),r.on("ping",V),r.on("pong",G),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",z),e.on("data",J),e.on("end",Y),e.on("error",X),this._readyState=A.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING){D(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===A.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=A.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){U(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||_,t,s)}pong(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){U(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||_,t,s)}resume(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){U(this,e,s);return}let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[p.extensionName]||(r.compress=!1),this._sender.send(e||_,r,s)}terminate(){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING){D(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=A.CLOSING,this._socket.destroy())}}}function B(e,t){e._readyState=A.CLOSING,e.emit("error",t),e.emitClose()}function I(e){return e.path=e.socketPath,o.connect(e)}function P(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function D(e,t,s){e._readyState=A.CLOSING;let r=Error(s);Error.captureStackTrace(r,D),t.setHeader?(t[F]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(B,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function U(e,t,s){if(t){let s=T(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${R[e.readyState]})`);process.nextTick(s,t)}}function j(e,t){let s=this[E];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[E]&&(s._socket.removeListener("data",J),process.nextTick(H,s._socket),1005===e?s.close():s.close(e,t))}function M(){let e=this[E];e.isPaused||e._socket.resume()}function q(e){let t=this[E];void 0!==t._socket[E]&&(t._socket.removeListener("data",J),process.nextTick(H,t._socket),t.close(e[v])),t.emit("error",e)}function $(){this[E].emitClose()}function W(e,t){this[E].emit("message",e,t)}function V(e){let t=this[E];t._autoPong&&t.pong(e,!this._isServer,w),t.emit("ping",e)}function G(e){this[E].emit("pong",e)}function H(e){e.resume()}function z(){let e;let t=this[E];this.removeListener("close",z),this.removeListener("data",J),this.removeListener("end",Y),t._readyState=A.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[E]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",$),t._receiver.on("finish",$))}function J(e){this[E]._receiver.write(e)||this.pause()}function Y(){let e=this[E];e._readyState=A.CLOSING,e._receiver.end(),this.end()}function X(){let e=this[E];this.removeListener("error",X),this.on("error",w),e&&(e._readyState=A.CLOSING,this.destroy())}Object.defineProperty(A,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(A.prototype,"CONNECTING",{enumerable:!0,value:R.indexOf("CONNECTING")}),Object.defineProperty(A,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(A.prototype,"OPEN",{enumerable:!0,value:R.indexOf("OPEN")}),Object.defineProperty(A,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(A.prototype,"CLOSING",{enumerable:!0,value:R.indexOf("CLOSING")}),Object.defineProperty(A,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),Object.defineProperty(A.prototype,"CLOSED",{enumerable:!0,value:R.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(A.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(A.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[C];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),A.prototype.addEventListener=k,A.prototype.removeEventListener=S,e.exports=A},3285:e=>{"use strict";e.exports=(e,t=process.argv)=>{let s=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(s+e),i=t.indexOf("--");return -1!==r&&(-1===i||r<i)}},7914:e=>{function t(e,t,s,r){return Math.round(e/s)+" "+r+(t>=1.5*s?"s":"")}e.exports=function(e,s){s=s||{};var r,i,n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var s=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*s;case"weeks":case"week":case"w":return 6048e5*s;case"days":case"day":case"d":return 864e5*s;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*s;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*s;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*s;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}(e);if("number"===n&&isFinite(e))return s.long?(r=Math.abs(e))>=864e5?t(e,r,864e5,"day"):r>=36e5?t(e,r,36e5,"hour"):r>=6e4?t(e,r,6e4,"minute"):r>=1e3?t(e,r,1e3,"second"):e+" ms":(i=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":i>=36e5?Math.round(e/36e5)+"h":i>=6e4?Math.round(e/6e4)+"m":i>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},1503:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(3504)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},3504:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7914),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},6183:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(1503):e.exports=s(2041)},2041:(e,t,s)=>{let r=s(4175),i=s(1764);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(7495);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(3504)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},8684:(e,t,s)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let s="color: "+this.color;t.splice(1,0,s,"color: inherit");let r=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(i=r))}),t.splice(i,0,s)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=s(281)(t);let{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},281:(e,t,s)=>{e.exports=function(e){function t(e){let s,i,n;let o=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),i=r-(s||r);a.diff=i,a.prev=s,a.curr=r,s=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let n=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(s,r)=>{if("%%"===s)return"%";n++;let i=t.formatters[r];if("function"==typeof i){let t=e[n];s=i.call(a,t),e.splice(n,1),n--}return s}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,n=t.enabled(e)),n),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,s){let r=t(this.namespace+(void 0===s?":":s)+e);return r.log=this.log,r}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let s;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(s=0;s<i;s++)r[s]&&("-"===(e=r[s].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let s,r;if("*"===e[e.length-1])return!0;for(s=0,r=t.skips.length;s<r;s++)if(t.skips[s].test(e))return!1;for(s=0,r=t.names.length;s<r;s++)if(t.names[s].test(e))return!0;return!1},t.humanize=s(7914),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(s=>{t[s]=e[s]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let s=0;for(let t=0;t<e.length;t++)s=(s<<5)-s+e.charCodeAt(t)|0;return t.colors[Math.abs(s)%t.colors.length]},t.enable(t.load()),t}},6610:(e,t,s)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=s(8684):e.exports=s(1369)},1369:(e,t,s)=>{let r=s(4175),i=s(1764);t.init=function(e){e.inspectOpts={};let s=Object.keys(t.inspectOpts);for(let r=0;r<s.length;r++)e.inspectOpts[s[r]]=t.inspectOpts[s[r]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(s){let{namespace:r,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),n=`  ${i};1m${r} \u001B[0m`;s[0]=n+s[0].split("\n").join("\n"+n),s.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else s[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+r+" "+s[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:r.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=s(7495);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let s=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[s]=r,e},{}),e.exports=s(281)(t);let{formatters:n}=e.exports;n.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},n.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},7495:(e,t,s)=>{"use strict";let r;let i=s(9801),n=s(4175),o=s(3285),{env:a}=process;function c(e,t={}){var s;return 0!==(s=function(e,{streamIsTTY:t,sniffFlags:s=!0}={}){let n=function(){if("FORCE_COLOR"in a)return"true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(Number.parseInt(a.FORCE_COLOR,10),3)}();void 0!==n&&(r=n);let c=s?r:n;if(0===c)return 0;if(s){if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2}if(e&&!t&&void 0===c)return 0;let h=c||0;if("dumb"===a.TERM)return h;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:h;if("TEAMCITY_VERSION"in a)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION)?1:0;if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=Number.parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:h}(e,{streamIsTTY:e&&e.isTTY,...t}))&&{level:s,hasBasic:!0,has256:s>=2,has16m:s>=3}}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?r=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(r=1),e.exports={supportsColor:c,stdout:c({isTTY:n.isatty(1)}),stderr:c({isTTY:n.isatty(2)})}},3434:(e,t,s)=>{var r=s(2048),i=s(7360),n=s(1282).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,c=s(2615),h=s(8791),l={},u=!1,d={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},p=Object.assign({},d),f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],g=["TRACE","TRACK","CONNECT"],m=!1,_=!1,y=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,s,r,i){if(this.abort(),_=!1,y=!1,!(e&&-1===g.indexOf(e)))throw Error("SecurityError: Request method not allowed");l={method:e,url:t.toString(),async:"boolean"!=typeof s||s,user:r||null,password:i||null},C(this.OPENED)},this.setDisableHeaderCheck=function(e){u=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!u&&(!e||-1!==f.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(m)throw Error("INVALID_STATE_ERR: send flag is true");return p[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!_?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||_)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&p[e]?p[e]:""},this.send=function(s){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(m)throw Error("INVALID_STATE_ERR: send has already been called");var u,d=!1,f=!1,g=i.parse(l.url);switch(g.protocol){case"https:":d=!0;case"http:":u=g.hostname;break;case"file:":f=!0;break;case void 0:case"":u="localhost";break;default:throw Error("Protocol not supported.")}if(f){if("GET"!==l.method)throw Error("XMLHttpRequest: Only GET method is supported");if(l.async)r.readFile(unescape(g.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,C(a.DONE))});else try{this.response=r.readFileSync(unescape(g.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,C(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var y=g.port||(d?443:80),b=g.pathname+(g.search?g.search:"");if(p.Host=u,d&&443===y||80===y||(p.Host+=":"+g.port),l.user){void 0===l.password&&(l.password="");var v=new Buffer(l.user+":"+l.password);p.Authorization="Basic "+v.toString("base64")}"GET"===l.method||"HEAD"===l.method?s=null:s?(p["Content-Length"]=Buffer.isBuffer(s)?s.length:Buffer.byteLength(s),Object.keys(p).some(function(e){return"content-type"===e.toLowerCase()})||(p["Content-Type"]="text/plain;charset=UTF-8")):"POST"===l.method&&(p["Content-Length"]=0);var E=e.agent||!1,w={host:u,port:y,path:b,method:l.method,headers:p,agent:E};if(d&&(w.pfx=e.pfx,w.key=e.key,w.passphrase=e.passphrase,w.cert=e.cert,w.ca=e.ca,w.ciphers=e.ciphers,w.rejectUnauthorized=!1!==e.rejectUnauthorized),_=!1,l.async){var k=d?h.request:c.request;m=!0,a.dispatchEvent("readystatechange");var S=function(s){if(302===(o=s).statusCode||303===o.statusCode||307===o.statusCode){l.url=o.headers.location;var r=i.parse(l.url);u=r.hostname;var n={hostname:r.hostname,port:r.port,path:r.path,method:303===o.statusCode?"GET":l.method,headers:p};d&&(n.pfx=e.pfx,n.key=e.key,n.passphrase=e.passphrase,n.cert=e.cert,n.ca=e.ca,n.ciphers=e.ciphers,n.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=k(n,S).on("error",x)).end();return}C(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}m&&C(a.LOADING)}),o.on("end",function(){m&&(m=!1,C(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},x=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return k(w,S).on("error",x);a.handleError(e)};t=k(w,S).on("error",x),e.autoUnref&&t.on("socket",e=>{e.unref()}),s&&t.write(s),t.end(),a.dispatchEvent("loadstart")}else{var O=".node-xmlhttprequest-content-"+process.pid,T=".node-xmlhttprequest-sync-"+process.pid;r.writeFileSync(T,"","utf8");for(var F="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(d?"s":"")+".request;var options = "+JSON.stringify(w)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+O+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+T+"');});response.on('error', function(error) {fs.writeFileSync('"+O+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+T+"');});}).on('error', function(error) {fs.writeFileSync('"+O+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+T+"');});"+(s?"req.write('"+JSON.stringify(s).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",N=n(process.argv[0],["-e",F]);r.existsSync(T););if(a.responseText=r.readFileSync(O,"utf8"),N.stdin.end(),r.unlinkSync(O),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var R=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(R,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var L=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:L.data.headers},a.responseText=L.data.text,a.response=Buffer.from(L.data.data,"base64"),C(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,_=!0,C(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),p=Object.assign({},d),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),_=y=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||m)&&this.readyState!==this.DONE&&(m=!1,C(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in b||(b[e]=[]),b[e].push(t)},this.removeEventListener=function(e,t){e in b&&(b[e]=b[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&l.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in b)for(let t=0,s=b[e].length;t<s;t++)this.readyState===this.DONE?setTimeout(function(){b[e][t].call(a)},0):b[e][t].call(a)};var C=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!y)&&(a.readyState=e,(l.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=y?"abort":_?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},7432:(e,t,s)=>{"use strict";let r,i;s.d(t,{io:()=>eR});var n,o={};s.r(o),s.d(o,{Decoder:()=>eC,Encoder:()=>ey,PacketType:()=>n,protocol:()=>e_});var a=s(3434),c=s.t(a,2);let h=Object.create(null);h.open="0",h.close="1",h.ping="2",h.pong="3",h.message="4",h.upgrade="5",h.noop="6";let l=Object.create(null);Object.keys(h).forEach(e=>{l[h[e]]=e});let u={type:"error",data:"parser error"},d=({type:e,data:t},s,r)=>r(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?s?t:"b"+p(t,!0).toString("base64"):h[e]+(t||"")),p=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),f=(e,t)=>{if("string"!=typeof e)return{type:"message",data:g(e,t)};let s=e.charAt(0);return"b"===s?{type:"message",data:g(Buffer.from(e.substring(1),"base64"),t)}:l[s]?e.length>1?{type:l[s],data:e.substring(1)}:{type:l[s]}:u},g=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),m=(e,t)=>{let s=e.length,r=Array(s),i=0;e.forEach((e,n)=>{d(e,!1,e=>{r[n]=e,++i===s&&t(r.join("\x1e"))})})},_=(e,t)=>{let s=e.split("\x1e"),r=[];for(let e=0;e<s.length;e++){let i=f(s[e],t);if(r.push(i),"error"===i.type)break}return r};function y(e){return e.reduce((e,t)=>e+t.length,0)}function b(e,t){if(e[0].length===t)return e.shift();let s=new Uint8Array(t),r=0;for(let i=0;i<t;i++)s[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),s}function C(e){if(e)return function(e){for(var t in C.prototype)e[t]=C.prototype[t];return e}(e)}C.prototype.on=C.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},C.prototype.once=function(e,t){function s(){this.off(e,s),t.apply(this,arguments)}return s.fn=t,this.on(e,s),this},C.prototype.off=C.prototype.removeListener=C.prototype.removeAllListeners=C.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var s,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((s=r[i])===t||s.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},C.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),s=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(s){s=s.slice(0);for(var r=0,i=s.length;r<i;++r)s[r].apply(this,t)}return this},C.prototype.emitReserved=C.prototype.emit,C.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},C.prototype.hasListeners=function(e){return!!this.listeners(e).length};let v=process.nextTick,E=global;class w{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),s=t[0].indexOf("=");if(-1===s)return;let r=t[0].substring(0,s).trim();if(!r.length)return;let i=t[0].substring(s+1).trim();34===i.charCodeAt(0)&&(i=i.slice(1,-1));let n={name:r,value:i};for(let e=1;e<t.length;e++){let s=t[e].split("=");if(2!==s.length)continue;let r=s[0].trim(),i=s[1].trim();switch(r){case"Expires":n.expires=new Date(i);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(i,10)),n.expires=o}}return n}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,s)=>{var r;(null===(r=t.expires)||void 0===r?void 0:r.getTime())<e&&this._cookies.delete(s)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,s]of this.cookies)t.push(`${e}=${s.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,s]of this.cookies)e.append("cookie",`${t}=${s.value}`)}}function k(e,...t){return t.reduce((t,s)=>(e.hasOwnProperty(s)&&(t[s]=e[s]),t),{})}let S=E.setTimeout,x=E.clearTimeout;function O(e,t){t.useNativeTimers?(e.setTimeoutFn=S.bind(E),e.clearTimeoutFn=x.bind(E)):(e.setTimeoutFn=E.setTimeout.bind(E),e.clearTimeoutFn=E.clearTimeout.bind(E))}function T(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var F=s(614);let N=F("engine.io-client:transport");class R extends Error{constructor(e,t,s){super(e),this.description=t,this.context=s,this.type="TransportError"}}class L extends C{constructor(e){super(),this.writable=!1,O(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,s){return super.emitReserved("error",new R(e,t,s)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):N("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=f(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let s in e)e.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t}(e);return t.length?"?"+t:""}}let A=F("engine.io-client:polling");class B extends L{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{A("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(A("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){A("pre-pause polling complete"),--e||t()})),this.writable||(A("we are currently writing - waiting to pause"),e++,this.once("drain",function(){A("pre-pause writing complete"),--e||t()}))}else t()}_poll(){A("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){A("polling got data %s",e),_(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():A('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{A("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(A("transport open - closing"),e()):(A("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,m(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=T()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let I=!1;try{I="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let P=I,D=F("engine.io-client:polling");function U(){}class j extends B{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,s=location.port;s||(s=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||s!==e.port}}doWrite(e,t){let s=this.request({method:"POST",data:e});s.on("success",t),s.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){D("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class M extends C{constructor(e,t,s){super(),this.createRequest=e,O(this,s),this._opts=s,this._method=s.method||"GET",this._uri=t,this._data=void 0!==s.data?s.data:null,this._create()}_create(){var e;let t=k(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let s=this._xhr=this.createRequest(t);try{D("xhr open %s: %s",this._method,this._uri),s.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in s.setDisableHeaderCheck&&s.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&s.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{s.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{s.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(s),"withCredentials"in s&&(s.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(s.timeout=this._opts.requestTimeout),s.onreadystatechange=()=>{var e;3===s.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(s.getResponseHeader("set-cookie"))),4===s.readyState&&(200===s.status||1223===s.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof s.status?s.status:0)},0))},D("xhr data %s",this._data),s.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=M.requestsCount++,M.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=U,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete M.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function q(){for(let e in M.requests)M.requests.hasOwnProperty(e)&&M.requests[e].abort()}M.requestsCount=0,M.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",q):"function"==typeof addEventListener&&addEventListener("onpagehide"in E?"pagehide":"unload",q,!1)),function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||P))return new XMLHttpRequest}catch(e){}if(!t)try{return new E[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let $=a||c;class W extends j{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null===(t=this.socket)||void 0===t?void 0:t._cookieJar},this.opts),new M(e=>new $(e),this.uri(),e)}}s(14),s(6292),s(4272);var V=s(608);s(7203);let G=F("engine.io-client:websocket"),H="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class z extends L{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,s=H?{}:k(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(s.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,s)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;d(s,this.supportsBinary,e=>{try{this.doWrite(s,e)}catch(e){G("websocket closed before onclose event")}r&&v(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=T()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}E.WebSocket||E.MozWebSocket;class J extends z{createSocket(e,t,s){var r;if(null===(r=this.socket)||void 0===r?void 0:r._cookieJar)for(let[e,t]of(s.headers=s.headers||{},s.headers.cookie="string"==typeof s.headers.cookie?[s.headers.cookie]:s.headers.cookie||[],this.socket._cookieJar.cookies))s.headers.cookie.push(`${e}=${t.value}`);return new V(e,t,s)}doWrite(e,t){let s={};e.options&&(s.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(s.compress=!1),this.ws.send(t,s)}}let Y=F("engine.io-client:webtransport");class X extends L{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{Y("transport closed gracefully"),this.onClose()}).catch(e=>{Y("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){i||(i=new TextDecoder);let s=[],r=0,n=-1,o=!1;return new TransformStream({transform(a,c){for(s.push(a);;){if(0===r){if(1>y(s))break;let e=b(s,1);o=(128&e[0])==128,r=(n=127&e[0])<126?3:126===n?1:2}else if(1===r){if(2>y(s))break;let e=b(s,2);n=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(8>y(s))break;let e=b(s,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){c.enqueue(u);break}n=4294967296*i+t.getUint32(4),r=3}else{if(y(s)<n)break;let e=b(s,n);c.enqueue(f(o?e:i.decode(e),t)),r=0}if(0===n||n>e){c.enqueue(u);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),s=e.readable.pipeThrough(t).getReader(),n=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(p(e.data,!1));d(e,!0,e=>{r||(r=new TextEncoder),t(r.encode(e))})}(e,s=>{let r;let i=s.length;if(i<126)new DataView((r=new Uint8Array(1)).buffer).setUint8(0,i);else if(i<65536){let e=new DataView((r=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,i)}else{let e=new DataView((r=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(i))}e.data&&"string"!=typeof e.data&&(r[0]|=128),t.enqueue(r),t.enqueue(s)})}});n.readable.pipeTo(e.writable),this._writer=n.writable.getWriter();let o=()=>{s.read().then(({done:e,value:t})=>{if(e){Y("session is closed");return}Y("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{Y("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let s=e[t],r=t===e.length-1;this._writer.write(s).then(()=>{r&&v(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let K={websocket:J,webtransport:X,polling:W},Q=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Z=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ee(e){if(e.length>8e3)throw"URI too long";let t=e,s=e.indexOf("["),r=e.indexOf("]");-1!=s&&-1!=r&&(e=e.substring(0,s)+e.substring(s,r).replace(/:/g,";")+e.substring(r,e.length));let i=Q.exec(e||""),n={},o=14;for(;o--;)n[Z[o]]=i[o]||"";return -1!=s&&-1!=r&&(n.source=t,n.host=n.host.substring(1,n.host.length-1).replace(/;/g,":"),n.authority=n.authority.replace("[","").replace("]","").replace(/;/g,":"),n.ipv6uri=!0),n.pathNames=function(e,t){let s=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&s.splice(0,1),"/"==t.slice(-1)&&s.splice(s.length-1,1),s}(0,n.path),n.queryKey=function(e,t){let s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(s[t]=r)}),s}(0,n.query),n}let et=F("engine.io-client:socket"),es="function"==typeof addEventListener&&"function"==typeof removeEventListener,er=[];es&&addEventListener("offline",()=>{et("closing %d connection(s) because the network was lost",er.length),er.forEach(e=>e())},!1);class ei extends C{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let s=ee(e);t.hostname=s.host,t.secure="https"===s.protocol||"wss"===s.protocol,t.port=s.port,s.query&&(t.query=s.query)}else t.host&&(t.hostname=ee(t.host).host);O(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},s=e.split("&");for(let e=0,r=s.length;e<r;e++){let r=s[e].split("=");t[decodeURIComponent(r[0])]=decodeURIComponent(r[1])}return t}(this.opts.query)),es&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(et("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},er.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new w),this._open()}createTransport(e){et('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let s=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return et("options: %j",s),new this._transportsByName[e](s)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&ei.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){et("setting transport %s",e.name),this.transport&&(et("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){et("socket open"),this.readyState="open",ei.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(et('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else et('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();et("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let s=this.writeBuffer[t].data;if(s&&(e+="string"==typeof s?function(e){let t=0,s=0;for(let r=0,i=e.length;r<i;r++)(t=e.charCodeAt(r))<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(r++,s+=4);return s}(s):Math.ceil(1.33*(s.byteLength||s.size))),t>0&&e>this._maxPayload)return et("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return et("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(et("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,v(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,s){return this._sendPacket("message",e,t,s),this}send(e,t,s){return this._sendPacket("message",e,t,s),this}_sendPacket(e,t,s,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof s&&(r=s,s=null),"closing"===this.readyState||"closed"===this.readyState)return;(s=s||{}).compress=!1!==s.compress;let i={type:e,data:t,options:s};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){let e=()=>{this._onClose("forced close"),et("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},s=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?s():e()}):this.upgrading?s():e()),this}_onError(e){if(et("socket error %j",e),ei.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return et("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(et('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),es&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=er.indexOf(this._offlineEventListener);-1!==e&&(et("removing listener for the 'offline' event"),er.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ei.protocol=4;class en extends ei{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){et("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){et('probing transport "%s"',e);let t=this.createTransport(e),s=!1;ei.priorWebsocketSuccess=!1;let r=()=>{s||(et('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",r=>{if(!s){if("pong"===r.type&&"probe"===r.data)et('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),t&&(ei.priorWebsocketSuccess="websocket"===t.name,et('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{s||"closed"===this.readyState||(et("changing transport and sending upgrade packet"),h(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{et('probe transport "%s" failed',e);let s=Error("probe error");s.transport=t.name,this.emitReserved("upgradeError",s)}}}))};function i(){s||(s=!0,h(),t.close(),t=null)}let n=s=>{let r=Error("probe error: "+s);r.transport=t.name,i(),et('probe transport "%s" failed because of error: %s',e,s),this.emitReserved("upgradeError",r)};function o(){n("transport closed")}function a(){n("socket closed")}function c(e){t&&e.name!==t.name&&(et('"%s" works - aborting "%s"',e.name,t.name),i())}let h=()=>{t.removeListener("open",r),t.removeListener("error",n),t.removeListener("close",o),this.off("close",a),this.off("upgrading",c)};t.once("open",r),t.once("error",n),t.once("close",o),this.once("close",a),this.once("upgrading",c),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{s||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let s=0;s<e.length;s++)~this.transports.indexOf(e[s])&&t.push(e[s]);return t}}class eo extends en{constructor(e,t={}){let s="object"==typeof e?e:t;(!s.transports||s.transports&&"string"==typeof s.transports[0])&&(s.transports=(s.transports||["polling","websocket","webtransport"]).map(e=>K[e]).filter(e=>!!e)),super(e,s)}}eo.protocol;var ea=s(6183);let ec=ea("socket.io-client:url"),eh="function"==typeof ArrayBuffer,el=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eu=Object.prototype.toString,ed="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eu.call(Blob),ep="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eu.call(File);function ef(e){return eh&&(e instanceof ArrayBuffer||el(e))||ed&&e instanceof Blob||ep&&e instanceof File}let eg=s(6610)("socket.io-parser"),em=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],e_=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(n||(n={}));class ey{constructor(e){this.replacer=e}encode(e){return(eg("encoding packet %j",e),(e.type===n.EVENT||e.type===n.ACK)&&function e(t,s){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let s=0,r=t.length;s<r;s++)if(e(t[s]))return!0;return!1}if(ef(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&e(t[s]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===n.EVENT?n.BINARY_EVENT:n.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===n.BINARY_EVENT||e.type===n.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),eg("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],s=e.data;return e.data=function e(t,s){if(!t)return t;if(ef(t)){let e={_placeholder:!0,num:s.length};return s.push(t),e}if(Array.isArray(t)){let r=Array(t.length);for(let i=0;i<t.length;i++)r[i]=e(t[i],s);return r}if("object"==typeof t&&!(t instanceof Date)){let r={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(r[i]=e(t[i],s));return r}return t}(s,t),e.attachments=t.length,{packet:e,buffers:t}}(e),s=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(s),r}}function eb(e){return"[object Object]"===Object.prototype.toString.call(e)}class eC extends C{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let s=(t=this.decodeString(e)).type===n.BINARY_EVENT;s||t.type===n.BINARY_ACK?(t.type=s?n.EVENT:n.ACK,this.reconstructor=new ev(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ef(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,s={type:Number(e.charAt(0))};if(void 0===n[s.type])throw Error("unknown packet type "+s.type);if(s.type===n.BINARY_EVENT||s.type===n.BINARY_ACK){let r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let i=e.substring(r,t);if(i!=Number(i)||"-"!==e.charAt(t))throw Error("Illegal attachments");s.attachments=Number(i)}if("/"===e.charAt(t+1)){let r=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);s.nsp=e.substring(r,t)}else s.nsp="/";let r=e.charAt(t+1);if(""!==r&&Number(r)==r){let r=t+1;for(;++t;){let s=e.charAt(t);if(null==s||Number(s)!=s){--t;break}if(t===e.length)break}s.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){let r=this.tryParse(e.substr(t));if(eC.isPayloadValid(s.type,r))s.data=r;else throw Error("invalid payload")}return eg("decoded %s as %j",e,s),s}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case n.CONNECT:return eb(t);case n.DISCONNECT:return void 0===t;case n.CONNECT_ERROR:return"string"==typeof t||eb(t);case n.EVENT:case n.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===em.indexOf(t[0]));case n.ACK:case n.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ev{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,s;let e=(t=this.reconPack,s=this.buffers,t.data=function e(t,s){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<s.length)return s[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let r=0;r<t.length;r++)t[r]=e(t[r],s);else if("object"==typeof t)for(let r in t)Object.prototype.hasOwnProperty.call(t,r)&&(t[r]=e(t[r],s));return t}(t.data,s),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function eE(e,t,s){return e.on(t,s),function(){e.off(t,s)}}let ew=ea("socket.io-client:socket"),ek=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class eS extends C{constructor(e,t,s){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,s&&s.auth&&(this.auth=s.auth),this._opts=Object.assign({},s),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[eE(e,"open",this.onopen.bind(this)),eE(e,"packet",this.onpacket.bind(this)),eE(e,"error",this.onerror.bind(this)),eE(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var s,r,i;if(ek.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:n.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;ew("emitting packet with ack id %d",e);let s=t.pop();this._registerAckCallback(e,s),o.id=e}let a=null===(r=null===(s=this.io.engine)||void 0===s?void 0:s.transport)||void 0===r?void 0:r.writable,c=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a?ew("discard packet as the transport is not currently writable"):c?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o),this.flags={},this}_registerAckCallback(e,t){var s;let r=null!==(s=this.flags.timeout)&&void 0!==s?s:this._opts.ackTimeout;if(void 0===r){this.acks[e]=t;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(ew("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));ew("event with ack id %d has timed out after %d ms",e,r),t.call(this,Error("operation has timed out"))},r),n=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};n.withError=!0,this.acks[e]=n}emitWithAck(e,...t){return new Promise((s,r)=>{let i=(e,t)=>e?r(e):s(t);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let s={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...r)=>{if(s===this._queue[0])return null!==e?s.tryCount>this._opts.retries&&(ew("packet [%d] is discarded after %d tries",s.id,s.tryCount),this._queue.shift(),t&&t(e)):(ew("packet [%d] was successfully sent",s.id),this._queue.shift(),t&&t(null,...r)),s.pending=!1,this._drainQueue()}),this._queue.push(s),this._drainQueue()}_drainQueue(e=!1){if(ew("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e){ew("packet [%d] has already been sent and is waiting for an ack",t.id);return}t.pending=!0,t.tryCount++,ew("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){ew("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:n.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){ew("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(!(e.nsp!==this.nsp))switch(e.type){case n.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case n.EVENT:case n.BINARY_EVENT:this.onevent(e);break;case n.ACK:case n.BINARY_ACK:this.onack(e);break;case n.DISCONNECT:this.ondisconnect();break;case n.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];ew("emitting event %j",t),null!=e.id&&(ew("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,s=!1;return function(...r){s||(s=!0,ew("sending ack %j",r),t.packet({type:n.ACK,id:e,data:r}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t){ew("bad ack %s",e.id);return}delete this.acks[e.id],ew("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){ew("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){ew("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(ew("performing disconnect (%s)",this.nsp),this.packet({type:n.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let s=0;s<t.length;s++)if(e===t[s]){t.splice(s,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function ex(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ex.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-s:e+s}return 0|Math.min(e,this.max)},ex.prototype.reset=function(){this.attempts=0},ex.prototype.setMin=function(e){this.ms=e},ex.prototype.setMax=function(e){this.max=e},ex.prototype.setJitter=function(e){this.jitter=e};let eO=ea("socket.io-client:manager");class eT extends C{constructor(e,t){var s;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,O(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(s=t.randomizationFactor)&&void 0!==s?s:.5),this.backoff=new ex({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let r=t.parser||o;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(eO("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;eO("opening %s",this.uri),this.engine=new eo(this.uri,this.opts);let t=this.engine,s=this;this._readyState="opening",this.skipReconnect=!1;let r=eE(t,"open",function(){s.onopen(),e&&e()}),i=t=>{eO("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},n=eE(t,"error",i);if(!1!==this._timeout){let e=this._timeout;eO("connect attempt will timeout after %d",e);let s=this.setTimeoutFn(()=>{eO("connect attempt timed out after %d",e),r(),i(Error("timeout")),t.close()},e);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}return this.subs.push(r),this.subs.push(n),this}connect(e){return this.open(e)}onopen(){eO("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(eE(e,"ping",this.onping.bind(this)),eE(e,"data",this.ondata.bind(this)),eE(e,"error",this.onerror.bind(this)),eE(e,"close",this.onclose.bind(this)),eE(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){v(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){eO("error",e),this.emitReserved("error",e)}socket(e,t){let s=this.nsps[e];return s?this._autoConnect&&!s.active&&s.connect():(s=new eS(this,e,t),this.nsps[e]=s),s}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active){eO("socket %s is still active, skipping close",e);return}this._close()}_packet(e){eO("writing packet %j",e);let t=this.encoder.encode(e);for(let s=0;s<t.length;s++)this.engine.write(t[s],e.options)}cleanup(){eO("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){eO("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var s;eO("closed due to %s",e),this.cleanup(),null===(s=this.engine)||void 0===s||s.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)eO("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();eO("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let s=this.setTimeoutFn(()=>{!e.skipReconnect&&(eO("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(eO("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(eO("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&s.unref(),this.subs.push(()=>{this.clearTimeoutFn(s)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eF=ea("socket.io-client"),eN={};function eR(e,t){let s;"object"==typeof e&&(t=e,e=void 0);let r=function(e,t="",s){let r=e;s=s||"undefined"!=typeof location&&location,null==e&&(e=s.protocol+"//"+s.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?s.protocol+e:s.host+e),/^(https?|wss?):\/\//.test(e)||(ec("protocol-less url %s",e),e=void 0!==s?s.protocol+"//"+e:"https://"+e),ec("parse %s",e),r=ee(e)),!r.port&&(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";let i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+t,r.href=r.protocol+"://"+i+(s&&s.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),i=r.source,n=r.id,o=r.path,a=eN[n]&&o in eN[n].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(eF("ignoring socket cache for %s",i),s=new eT(i,t)):(eN[n]||(eF("new io instance for %s",i),eN[n]=new eT(i,t)),s=eN[n]),r.query&&!t.query&&(t.query=r.queryKey),s.socket(r.path,t)}Object.assign(eR,{Manager:eT,Socket:eS,io:eR,connect:eR})}};